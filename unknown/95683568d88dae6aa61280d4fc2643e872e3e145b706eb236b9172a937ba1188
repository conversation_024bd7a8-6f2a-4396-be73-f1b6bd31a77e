{"Gift Card - Send Email At 2": "", "Any specific requirements or questions": "", "Komplikationen bei bisheriger Vollnarkose?": "", "Gift For": "", "Gift Card Message": "", "Medikation/Dosierung Selbstangabe:": "", "Wie dürfen wir mit Ihnen Kontakt aufnehmen?": "", "Last appointment services": "", "Last appointment categories": "", "IBAN": "", "Total appointments booked for BT_AUG": "", "What is your current booking system": "", "Gift Card QR Code Url": "", "Frühere Operationen": "", "Google Click ID": "", "Augen (z.B Fehlsichtigkeit, Brillenträger)-Erkrankungen?": "", "UTM Campaign": "", "hello 2645 1234": "", "Name des Gynäkologen": "", "Gift Card Send Email": "", "Total appointments booked for BT_LIPO_SIE": "", "2. Telefonnummer": "", "Folgeskostenversicherung bereits vorhanden?": "", "Mitversichert bei (Name)": "", "Gift Card Quantity": "", "What is your budget for implementing new solutions": "", "Total appointments booked for BT_OSS_ALT": "", "Gift Card_Message": "", "Autoimmunerkrankungen": "", "Are you prepared to make an investment today to revolutionize your business": "", "What is your website URL": "", "Newsletter erwünscht": "", "Gift Card Street Address": "", "Empfohlen von": "", "Gift Card - Send Email At 5": "", "UTM Referrer": "", "Total appointments booked for OP_ITN_IMPL": "", "Total appointments booked for Hautanalyse": "", "Total appointments booked for Blutabnahme": "", "Gift Card Recipient City": "", "Familiärer Brustkrebs-Erkrankungen?": "", "Last appointment location": "", "Schwangerschaft ausgeschlossen?": "", "Magenballon/-Band-Erkrankungen?": "", "Frühere ästhetische Behandlungen?": "", "CliniCore Profile": "", "Patient ID": "", "Gift Card Recipient Address To": "", "Gift Card Price": "", "Total appointments booked for FU_LU": "", "UTM Source": "", "Bisherige Vollnarkose?": "", "Haut-Erkrankungen?": "", "Total appointments booked for BT_OLI_AKB": "", "Gift Card Street Address 2": "", "Gift Card Shipping": "", "Notiz": "", "Gift Card QR Code": "", "Sonstige Erkrankungen:": "", "Chronische Erkrankungen": "", "Nahrungsergänzungsmittel die blutverdünnend sind": "", "Gewicht (kg)": "", "Total appointments booked for BT_BDP": "", "Message": "", "Gift Card State": "", "Total appointments booked for BT_Nase_SS": "", "Total appointments booked for Bruststraffung": "", "Versichertenkategorie": "", "BIC": "", "Gift Card - Send Email At 4": "", "Versicherungsnummer": "", "Telefonnummer des Gynäkologen": "", "Titel": "", "Total appointments booked for BT_ENDO_LU": "", "Blutdruck-Erkrankungen?": "", "Privatversicherung": "", "Total appointments booked for OP-Nachsorge/FU": "", "hello 2645": "", "Zusatzversichert": "", "Gift Card Recipient Name": "", "UTM MatchType": "", "Telefon Privat": "", "Komplikationen bei ästhetische Behandlungen?": "", "Gift Card City": "", "Kontakt erlaubt": "", "hello": "", "How soon are you looking to implement our premium solutions": "", "BMI": "", "Total appointments booked for BT_OLI_ALT": "", "Allergien": "", "Lunge-Erkrankungen?": "", "Gift Card Recipient Email": "", "Coupon Code": "", "Titel (nachgestellt)": "", "hello2": "", "Größe (cm)": "", "Gift Card - Send Email At 3": "", "Neurologische Erkrankungen (z.b Epilepsie)-Erkrankungen?": "", "Gift Card Coupon Code": "", "Gift Card Shipping Price": "", "Tumorerkrankungen/Krebs-Erkrankungen?": "", "Regelmäßge Medikation(Selbstangabe)?": "", "Newsletter erwünscht für Rabatt Aktionen ": "", "Zustimmung WAHonline": "", "Krankenversicherung": "", "Last appointment treated by": "", "Dauerdiagnosen": "", "Gift Card Send Method": "", "Interessiert an": "", "Last appointment resource": "", "Niere-Erkrankungen?": "", "Beruf": "", "Gift Card Recipient State": "", "Total appointments booked for BT_LIPO_ALT": "", "Rheuma/Arthritis-Erkrankungen?": "", "Infektionskrankheiten?": "", "What is your business type": "", "CC Patient ID": "", "AFFILIATE ID": "", "Gift Card Address To": "", "Gynäkologen?": "", "Hausarzt": "", "Komplikationen bei früheren Operationen?": "", "Psyche (z.B Depressionen)-Erkrankungen?": "", "Stoffwechsel (Schilddrüse/Diabetes/Gicht)-Erkrankungen?": "", "Herz-Kreislauf-Erkrankungen?": "", "Adresse des Hausarztes": "", "Knochen-Erkrankungen?": "", "Gift Card - Send Email At": "", "Blutgerinnung-Erkrankungen?": "", "Gift Card Recipient Street Address": "", "Gift Card Email scheduled date": "", "Gift Card Schedule Time": "", "Total appointments booked for BT_AUG_SIE": "", "Mitversichert bei (Nummer)": "", "Infektionserkrankungen (Hepatitis, HIV etc.)": "", "Total appointments booked for BT_BSTR_AKB": "", "UTM Content": "", "Telefon Geschäftlich": "", "Familienstand": "", "Gerinnungsstörungen mit Thromboseneigung oder Blutungsneigung": "", "Telefonnummer des Hausarztes": "", "Total appointments booked for BT_Nase_AKB": "", "FB Click ID": "", "Fift For": "", "Name des Hausarztes": "", "Wie sind Sie auf uns aufmerksam geworden?": "", "Total appointments booked for BT_BDP_AKB": "", "Corona Infektion?": "", "Life Time Value": "", "Gift Card Discount Code": "", "On a scale of 1-10, how ready are you to transform your business with our solutions?": "", "What premium features are you most interested in": "", "UTM Medium": "", "Medikation": "", "Adresse des Gynäkologen": "", "Total Appointments": "", "Corona Impfung?": "", "Gift Card Recipient Street Address 2": "", "How did you hear about us": "", "Custom Field": "", "Total Invoices": "", "Total Payments": "", "Due amount": "", "Credit amount": "", "Total Invoiced Amount": "", "Total Spent": "", "Average Invoice Amount": "", "Average Payment Amount": "", "Lifetime Value": "", "Last Invoice Date": "", "Zu uns gekommen durch": "", "Telefon Mobil": "+880 1750-690455", "Terminerinnerungen": "", "Allergie": "", "TEST 2 - multiple selection": "", "Test3 - Dropdown Multiple": "", "Befunde": "", "contact_id": "vC8u2lwVdBQPYjQOZaLK", "first_name": "Test a", "last_name": "Custom Fields", "full_name": "Test a Custom Fields", "email": "<EMAIL>", "tags": "", "address1": "Test Street", "city": "Test City", "country": "US", "date_created": "2025-07-26T00:31:52.985Z", "date_of_birth": "1990-01-01T00:00:00.000Z", "contact_source": "cc", "full_address": "Test Street, Test City ", "contact_type": "lead", "location": {"name": "Dhaka", "address": "5915 37th Avenue", "city": "Dhaka", "state": "NY", "country": "US", "postalCode": "11377", "fullAddress": "5915 37th Avenue, Dhaka NY 11377", "id": "CIY0QcIvP7m9TxVWlvy3"}, "workflow": {"id": "ffdf9f52-136e-4872-b072-582f3c267521", "name": "V4 Contact Update"}, "triggerData": {}, "customData": {}}