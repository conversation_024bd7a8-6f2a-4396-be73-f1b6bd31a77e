/**
 * AutoPatient Field Creation Utilities
 *
 * Specialized utilities for creating AutoPatient custom fields from
 * CliniCore field definitions. Handles AP-specific field creation
 * logic, validation, and error handling.
 *
 * @fileoverview AP-specific field creation utilities
 * @version 2.0.0
 * @since 2024-07-28
 */

import apiClient from "@apiClient";
import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import { logDebug, logError, logInfo, logWarn } from "@/utils/logger";
import { convertCcFieldToAp } from "../cc/fieldConverter";
import { generateUniqueFieldName } from "../conflict/compatibility";
import { storeMappingForCreatedFields } from "../database/operations";
import type { FieldCreationResult } from "../types";

/**
 * Create AutoPatient field from CliniCore field
 *
 * Creates a new custom field in AutoPatient based on a CliniCore field
 * definition. Handles field conversion, conflict resolution, and database
 * mapping storage with comprehensive error handling.
 *
 * @param ccField - CliniCore field to create in AutoPatient
 * @param isRetryAttempt - Whether this is a retry attempt during conflict resolution (affects logging behavior)
 * @returns Promise resolving to created AP field or null if creation failed
 *
 * @example
 * ```typescript
 * const ccField: GetCCCustomField = {
 *   id: 123,
 *   name: "patient_notes",
 *   label: "Patient Notes",
 *   type: "textarea"
 * };
 *
 * const apField = await createApFieldFromCc(ccField, "req-123");
 * if (apField) {
 *   console.log(`Created AP field: ${apField.name} (ID: ${apField.id})`);
 * }
 * ```
 *
 * @since 2.0.0
 */
export async function createApFieldFromCc(
	ccField: GetCCCustomField,
	isRetryAttempt?: boolean,
): Promise<APGetCustomFieldType | null> {
	try {
		// Convert CC field to AP format
		const apFieldData = convertCcFieldToAp(ccField);
		if (!apFieldData) {
			logWarn("CC field conversion returned null, skipping creation", {
				ccFieldId: ccField.id,
				ccFieldName: ccField.name,
				ccFieldType: ccField.type,
			});
			return null;
		}

		// Attempt to create the field
		const createdField = await apiClient.ap.apCustomfield.create(apFieldData);

		if (createdField) {
			// Single success log with essential information only
			logInfo("AP field created successfully", {
				ccFieldId: ccField.id,
				ccFieldName: ccField.name,
				apFieldId: createdField.id,
				apFieldName: createdField.name,
			});

			// Store the mapping in database
			await storeMappingForCreatedFields(createdField, ccField);

			return createdField;
		} else {
			logError("AP field creation returned null", {
				ccFieldId: ccField.id,
				ccFieldName: ccField.name,
			});
			return null;
		}
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);

		// Check if error is due to existing field conflict
		const isExistingFieldConflict =
			errorMessage.toLowerCase().includes("already exists") ||
			errorMessage.toLowerCase().includes("duplicate") ||
			errorMessage.toLowerCase().includes("conflict") ||
			errorMessage.toLowerCase().includes("name is already taken");

		if (isExistingFieldConflict) {
			if (!isRetryAttempt) {
				// First attempt - log as warning since conflict resolution will be attempted
				logWarn(
					"AP field creation conflict detected, attempting resolution with unique name",
					{
						ccFieldId: ccField.id,
						ccFieldName: ccField.name,
						error: errorMessage,
						action: "conflict_resolution_initiated",
					},
				);
			} else {
				// Retry attempt failed - this is a real error
				logError("AP field creation failed even with unique name", {
					ccFieldId: ccField.id,
					ccFieldName: ccField.name,
					error: errorMessage,
					action: "conflict_resolution_failed",
				});
			}

			// Try to find the existing field for mapping
			const existingField = await findApFieldByName(ccField.name);
			if (existingField) {
				logInfo("✓ AP field creation resolved by mapping to existing field", {
					ccFieldId: ccField.id,
					existingApFieldId: existingField.id,
					existingApFieldName: existingField.name,
					finalOutcome: "success",
					resolutionMethod: "existing_field_mapping",
				});

				// Store mapping to existing field
				await storeMappingForCreatedFields(existingField, ccField);
				return existingField;
			}
		} else if (!isRetryAttempt) {
			// Non-conflict error on first attempt
			logError("Failed to create AP field from CC field", {
				ccFieldId: ccField.id,
				ccFieldName: ccField.name,
				error: errorMessage,
				action: "creation_failed_non_conflict",
			});
		}

		return null;
	}
}

/**
 * Create AP field with conflict resolution
 *
 * Enhanced field creation function that handles name conflicts by
 * generating unique field names when necessary. Provides comprehensive
 * result tracking and error handling.
 *
 * @param ccField - CliniCore field to create in AutoPatient
 * @param existingApFields - Existing AP fields to check for conflicts
 * @returns Promise resolving to comprehensive creation result
 *
 * @example
 * ```typescript
 * const result = await createApFieldWithConflictResolution(
 *   ccField,
 *   existingApFields,
 *   "req-123"
 * );
 *
 * if (result.success && result.field) {
 *   console.log(`Successfully created field: ${result.field.name}`);
 * } else {
 *   console.error(`Creation failed: ${result.error}`);
 * }
 * ```
 *
 * @since 2.0.0
 */
export async function createApFieldWithConflictResolution(
	ccField: GetCCCustomField,
	existingApFields: APGetCustomFieldType[],
): Promise<FieldCreationResult> {
	try {
		// First try normal creation
		const createdField = await createApFieldFromCc(ccField, false);

		if (createdField) {
			return {
				success: true,
				field: createdField,
			};
		}

		// If creation failed, try with unique name
		const uniqueName = generateUniqueFieldName(
			ccField.name,
			ccField,
			"cc",
			existingApFields,
		);

		logInfo("Retrying AP field creation with unique name", {
			ccFieldId: ccField.id,
			originalName: ccField.name,
			uniqueName,
		});

		// Create modified CC field with unique name
		const modifiedCcField: GetCCCustomField = {
			...ccField,
			name: uniqueName,
			label: uniqueName,
		};

		const retryCreatedField = await createApFieldFromCc(modifiedCcField, true);

		if (retryCreatedField) {
			logInfo("✓ AP field creation successful after conflict resolution", {
				ccFieldId: ccField.id,
				originalName: ccField.name,
				uniqueName,
				apFieldId: retryCreatedField.id,
				finalOutcome: "success",
				resolutionMethod: "unique_naming",
			});

			return {
				success: true,
				field: retryCreatedField,
			};
		} else {
			logError(
				"✘ AP field creation failed - conflict resolution unsuccessful",
				{
					ccFieldId: ccField.id,
					originalName: ccField.name,
					uniqueName,
					finalOutcome: "failure",
					action: "conflict_resolution_failed",
				},
			);

			return {
				success: false,
				error: "Field creation failed even with unique name",
			};
		}
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		const isExistingFieldConflict =
			errorMessage.toLowerCase().includes("already exists") ||
			errorMessage.toLowerCase().includes("duplicate") ||
			errorMessage.toLowerCase().includes("conflict");

		return {
			success: false,
			error: errorMessage,
			existingFieldConflict: isExistingFieldConflict,
		};
	}
}

/**
 * Find existing AP field by name
 *
 * Searches through all AP custom fields to find one with the specified name.
 * Used for conflict resolution and field mapping operations.
 *
 * @param fieldName - Name of the field to search for
 * @returns Promise resolving to the existing AP field or null if not found
 *
 * @example
 * ```typescript
 * const existingField = await findApFieldByName("patient_notes", "req-123");
 * if (existingField) {
 *   console.log(`Found existing field: ${existingField.name} (ID: ${existingField.id})`);
 * }
 * ```
 *
 * @since 2.0.0
 */
export async function findApFieldByName(
	fieldName: string,
): Promise<APGetCustomFieldType | null> {
	try {
		logDebug("Searching for AP field by name", {
			fieldName,
		});

		// Fetch all AP fields to search through them
		const allApFields =
			await apiClient.ap.apCustomfield.allWithParentFilter(true); // Invalidate cache

		// Find field with matching name or fieldKey
		const existingField = allApFields.find(
			(field) => field.name === fieldName || field.fieldKey === fieldName,
		);

		if (existingField) {
			logDebug("Found existing AP field with matching name", {
				fieldName,
				existingFieldId: existingField.id,
				existingFieldName: existingField.name,
				existingFieldType: existingField.dataType,
			});
			return existingField;
		}

		logDebug("No existing AP field found with matching name", {
			fieldName,
			totalApFields: allApFields.length,
		});

		return null;
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		logError("Failed to search for AP field by name", {
			fieldName,
			error: errorMessage,
		});
		return null;
	}
}
