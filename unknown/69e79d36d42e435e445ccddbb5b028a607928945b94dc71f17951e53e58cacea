/**
 * Field Mapping Resolver
 *
 * Handles retrieval and resolution of custom field mappings from the database.
 * Provides utilities to find field mappings between AutoPatient and CliniCore
 * platforms for patient custom field value synchronization.
 *
 * @fileoverview Field mapping resolution utilities
 * @version 1.0.0
 * @since 2024-07-29
 */

import { dbSchema, getDb } from "@database";
import { eq } from "drizzle-orm";
import type { APGetCustomFieldType, GetCCCustomField } from "@/type";
import { logDebug, logError } from "@/utils/logger";
import type { FieldMapping } from "./types";

/**
 * Get all field mappings from database
 *
 * @returns Array of field mappings
 */
export async function getAllFieldMappings(): Promise<FieldMapping[]> {
	try {
		const db = getDb();
		const mappings = await db.select().from(dbSchema.customFields);

		const fieldMappings: FieldMapping[] = mappings
			.filter(
				(mapping) =>
					mapping.apId && mapping.ccId && mapping.apConfig && mapping.ccConfig,
			)
			.map((mapping) => ({
				apId: mapping.apId as string,
				ccId: mapping.ccId as number,
				apConfig: mapping.apConfig as APGetCustomFieldType,
				ccConfig: mapping.ccConfig as GetCCCustomField,
				mappingType: mapping.mappingType || "custom_to_custom",
			}));

		return fieldMappings;
	} catch (error) {
		logError("Failed to fetch field mappings from database", {
			error: String(error),
		});
		return [];
	}
}

/**
 * Get field mapping by AP field ID
 *
 * @param apFieldId - AutoPatient field ID
 * @returns Field mapping or null if not found
 */
export async function getFieldMappingByApId(
	apFieldId: string,
): Promise<FieldMapping | null> {
	try {
		logDebug("Fetching field mapping by AP field ID", {
			apFieldId,
		});

		const db = getDb();
		const mappings = await db
			.select()
			.from(dbSchema.customFields)
			.where(eq(dbSchema.customFields.apId, apFieldId));

		if (mappings.length === 0) {
			logDebug("No field mapping found for AP field ID", {
				apFieldId,
			});
			return null;
		}

		const mapping = mappings[0];
		if (!mapping.ccId || !mapping.apConfig || !mapping.ccConfig) {
			logError("Invalid field mapping data", {
				apFieldId,
				mapping: {
					ccId: mapping.ccId,
					hasApConfig: !!mapping.apConfig,
					hasCcConfig: !!mapping.ccConfig,
				},
			});
			return null;
		}

		const fieldMapping: FieldMapping = {
			apId: mapping.apId as string,
			ccId: mapping.ccId,
			apConfig: mapping.apConfig as APGetCustomFieldType,
			ccConfig: mapping.ccConfig as GetCCCustomField,
			mappingType: mapping.mappingType || "custom_to_custom",
		};

		logDebug("Found field mapping for AP field ID", {
			apFieldId,
			ccFieldId: fieldMapping.ccId,
			mappingType: fieldMapping.mappingType,
		});

		return fieldMapping;
	} catch (error) {
		logError("Failed to fetch field mapping by AP field ID", {
			apFieldId,
			error: String(error),
		});
		return null;
	}
}

/**
 * Get field mapping by CC field ID
 *
 * @param ccFieldId - CliniCore field ID
 * @returns Field mapping or null if not found
 */
export async function getFieldMappingByCcId(
	ccFieldId: number,
): Promise<FieldMapping | null> {
	try {
		logDebug("Fetching field mapping by CC field ID", {
			ccFieldId,
		});

		const db = getDb();
		const mappings = await db
			.select()
			.from(dbSchema.customFields)
			.where(eq(dbSchema.customFields.ccId, ccFieldId));

		if (mappings.length === 0) {
			logDebug("No field mapping found for CC field ID", {
				ccFieldId,
			});
			return null;
		}

		const mapping = mappings[0];
		if (!mapping.apId || !mapping.apConfig || !mapping.ccConfig) {
			logError("Invalid field mapping data", {
				ccFieldId,
				mapping: {
					apId: mapping.apId,
					hasApConfig: !!mapping.apConfig,
					hasCcConfig: !!mapping.ccConfig,
				},
			});
			return null;
		}

		const fieldMapping: FieldMapping = {
			apId: mapping.apId,
			ccId: mapping.ccId as number,
			apConfig: mapping.apConfig as APGetCustomFieldType,
			ccConfig: mapping.ccConfig as GetCCCustomField,
			mappingType: mapping.mappingType || "custom_to_custom",
		};

		logDebug("Found field mapping for CC field ID", {
			ccFieldId,
			apFieldId: fieldMapping.apId,
			mappingType: fieldMapping.mappingType,
		});

		return fieldMapping;
	} catch (error) {
		logError("Failed to fetch field mapping by CC field ID", {
			ccFieldId,
			error: String(error),
		});
		return null;
	}
}

/**
 * Create mapping lookup tables for efficient field resolution
 *
 * @param mappings - Array of field mappings
 * @returns Lookup tables for AP and CC field IDs
 */
export function createMappingLookupTables(mappings: FieldMapping[]): {
	apToCC: Map<string, FieldMapping>;
	ccToAP: Map<number, FieldMapping>;
} {
	const apToCC = new Map<string, FieldMapping>();
	const ccToAP = new Map<number, FieldMapping>();

	for (const mapping of mappings) {
		apToCC.set(mapping.apId, mapping);
		ccToAP.set(mapping.ccId, mapping);
	}

	return { apToCC, ccToAP };
}

/**
 * Filter mappings by mapping type
 *
 * @param mappings - Array of field mappings
 * @param mappingType - Type of mapping to filter by
 * @returns Filtered array of field mappings
 */
export function filterMappingsByType(
	mappings: FieldMapping[],
	mappingType: string,
): FieldMapping[] {
	return mappings.filter((mapping) => mapping.mappingType === mappingType);
}

/**
 * Get mapping statistics
 *
 * @param mappings - Array of field mappings
 * @returns Statistics about the mappings
 */
export function getMappingStatistics(mappings: FieldMapping[]): {
	total: number;
	byType: Record<string, number>;
	apFieldTypes: Record<string, number>;
	ccFieldTypes: Record<string, number>;
} {
	const stats = {
		total: mappings.length,
		byType: {} as Record<string, number>,
		apFieldTypes: {} as Record<string, number>,
		ccFieldTypes: {} as Record<string, number>,
	};

	for (const mapping of mappings) {
		// Count by mapping type
		stats.byType[mapping.mappingType] =
			(stats.byType[mapping.mappingType] || 0) + 1;

		// Count by AP field type
		const apType = mapping.apConfig.dataType;
		stats.apFieldTypes[apType] = (stats.apFieldTypes[apType] || 0) + 1;

		// Count by CC field type
		const ccType = mapping.ccConfig.type;
		stats.ccFieldTypes[ccType] = (stats.ccFieldTypes[ccType] || 0) + 1;
	}

	return stats;
}
