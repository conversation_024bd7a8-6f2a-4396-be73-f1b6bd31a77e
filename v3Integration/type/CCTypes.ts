type GetCCPatientType = {
  id: number
  createdAt: string
  updatedAt: string
  createdBy: number
  updatedBy: number
  firstName: string
  lastName: string
  dob: string | null
  ssn: string | null
  flashMessage: string
  active: boolean
  phoneMobile: string
  email: string
  title: string | null
  titleSuffix: string | null
  healthInsurance: string | null
  gender: string | null
  addresses: any[]
  categories: number[]
  customFields: number[]
  invoices: number[]
  payments: number[]
  files: number[]
  history: number[]
  appointments: number[]
  messages: any[]
  medications: any[]
  qrUrl: string
  avatarUrl: string | null
}

type PostCCPatientType = {
  firstName?: string
  lastName?: string
  dob?: string | null
  ssn?: string | null
  active?: boolean
  phoneMobile?: string
  email?: string
  gender?: string | null
  addresses?: any[]
  customFields?: number[]
}

type GetCCCustomField = {
  id: number
  name: string
  label: string
  validation: string
  type: string
  color: string | null
  positions: {
    id: number
    order: number
    tab: number
    customField: number
  }[]
  allowMultipleValues: boolean
  useCustomSort: boolean | null
  isRequired: boolean
  allowedValues: {
    id: number
    value: string
    createdAt: string | null
    updatedAt: string | null
    createdBy: string | null
    updatedBy: string | null
  }[]
  defaultValues: {
    id: number
    value: string | number | null
    createdAt: string | null
    updatedAt: string | null
    createdBy: string | null
    updatedBy: string | null
  }[]
}

type GetCCPatientCustomField = {
  id?: number
  values: {
    id?: number
    value?: string
    createdAt?: string
    updatedAt?: string
    createdBy?: number | null
    updatedBy?: number | null
  }[]
  field: GetCCCustomField
  patient?: number | null
}

type GetCCAppointmentType = {
  id: number
  uuid: string | null
  startsAt: string
  endsAt: string
  arrivedAt: string | null
  processedAt: string | null
  treatmentStartedAt: string | null
  allDay: boolean
  slot: boolean
  subject: string | null
  title: string
  firstOfPatient: boolean
  onPatientBirthday: boolean
  description: string
  color: string | null
  patientCategories: number[]
  patientsPreview: any[]
  patients: number[]
  people: number[]
  resources: number[]
  categories: number[]
  location?: number
  services: number[]
  series: number
  canceledWhy: string | null
  createdAt: string
  updatedAt: string
  canceledAt: string | null
  createdBy: number
  updatedBy: number
  canceledBy: number | null
  reminderAt: string | null
  reminderStatus: string
  reminderSentAt: string | null
  deletedAt: string | null
}

type PostCCAppointmentType = {
  startsAt?: string
  endsAt?: string
  allDay?: boolean
  slot?: boolean
  subject?: string | null
  title?: string | null
  onPatientBirthday?: boolean
  description?: string
  patients?: number[]
  people?: number[]
  resources?: number[]
  categories?: number[]
  location?: number
  services?: number[]
}

type PutCCAppointmentType = {
  startsAt?: string
  endsAt?: string
  allDay?: boolean
  slot?: boolean
  subject?: string | null
  title?: string
  onPatientBirthday?: boolean
  description?: string
  color?: string | null
  patientCategories?: number[]
  patientsPreview?: any[]
  patients?: number[]
  people?: number[]
  resources?: number[]
  categories?: number[]
  location?: number
  services?: number[]
  series?: number
  canceledWhy?: string | null
  createdAt?: string
  updatedAt?: string
  canceledAt?: string | null
  createdBy?: number
  updatedBy?: number
  canceledBy?: number | null
  reminderAt?: string | null
  reminderStatus?: string
  reminderSentAt?: string | null
  deletedAt?: string | null
}

type GetCCAppointmentCategory = {
  id: number
  title: string
  shortTitle?: string
  color?: string
  alwaysVisible?: boolean
  locked?: boolean
  blocking?: number
}

type GetCCServiceType = {
  id: number
  name: string
  externalName: string
  description: string | null
  additionalText: string | null
  gross: number
  taxRate: number
  duration: number
  ownCosts: number
  ownCostsPercent: number | null
  barcode: string | null
  accountingNumber: string
  costCenter: string | null
  productGroup: number
  packages: number[]
  resources: number[]
  invoiceProducts: number[]
  historyProducts: number[]
  products: number[]
  reminderActive: boolean
  notificationsActive: boolean
  automaticallyAddHistoryProducts: boolean
  booked: boolean
  bookingHoursInAdvance: number | null
  bookingSelfServiceInAdvance: number | null
  deletedAt: string | null
}

type GetCCUserType = {
  id: number
  firstName: string
  lastName: string
  shortName: string
  username: string
  externalName: string | null
  email: string
  color: string | null
  order: any | null
  defaultHistoryItemType: string
  invoiceNumberSequence: any | null
  setting: any | null
  costCenter: string | null
  category: any | null
  roles: {
    id: number
    slug: string
    description: string
    permissions: {
      id?: number
      slug: string
      description: string
    }[]
  }[]
  services: number[]
  diagnosiaToken: string
  deletedAt: string | null
  avatarUrl: string | null
  wahonlineAffiliateNumber: string | null
  wahonlineProfessionCode: string | null
  wahonlineFirstName: string | null
  wahonlineLastName: string | null
  locked: number
  lastLoginAt: string
  lastLoginIp: string
  successfulLoginCount: number
  twoFactorAuthEnabled: boolean
  twoFactorAuthEnforced: boolean
  passwordChangeRequired: boolean
}

type GetCCCustomfieldsType = {
  id: number
  name: string
  label: string
  validation: string
  type: string
  color: string | null
  positions: CustomfieldsPosition[]
  allowMultipleValues: boolean
  useCustomSort: boolean | null
  isRequired: boolean
  allowedValues: CustomfieldsAllowedValue[]
  defaultValues: CustomfieldsDefaultValue[]
}

interface CustomfieldsPosition {
  id: number
  order: number
  tab: number
  customField: number
}
interface CustomfieldsAllowedValue {
  id: number
  value: string
  createdAt: string | null
  updatedAt: string | null
  createdBy: string | null
  updatedBy: string | null
}
interface CustomfieldsDefaultValue {
  id: number
  value: string | number | null
  createdAt: string | null
  updatedAt: string | null
  createdBy: string | null
  updatedBy: string | null
}

type GetCCLocationType = {
  id: number
  name?: string
  shortName?: string
  street?: string
  streetNumber?: string
  postalCode?: string
  city?: string
  country?: string
  additionalInfo?: string
  email?: string
}

type GetCCPatientCategoryType = {
  id: number
  label: string
  color: string
  locked: number
}

type GetCCResourceType = {
  id: number
  name: string
  shortName: string | null
  location: number
}

type GetCCAppointmentCategoryType = {
  id: number
  title: string
  shortTitle: string
  color: string
  alwaysVisible: boolean
  locked: boolean
  blocking: number
}

type SocketCCPayment = {
  id: number
  paymentNumber: string
  gross: number
  customIdentification: null
  comment: string
  createdAt: string
  date: string
  updatedAt: string
  createdBy: number
  updatedBy: number
  register: number
  patient: number
  invoicePayments: { id: number; gross: number; invoice: number; payment: number }[]
  reversedBy: null
  reverses: null
  pdfUrl: string
  canceled: boolean
}

type SocketCCInvoice = {
  id: number
  invoiceNumber?: string
  createdAt?: string
  updatedAt?: string
  createdBy?: number
  updatedBy?: number
  addressText?: null
  discount?: number
  status?: string
  vatId?: null
  description?: string
  note?: null
  canceledWhy?: null
  settlementStatus?: null
  pdfUrl?: string
  invoiceNumberSequence?: string
  address?: {
    id?: number
    label?: null
    name?: string
    street?: null
    streetNumber?: null
    postalCode?: null
    city?: null
    country?: string
    primary?: number
  }
  appointment?: null
  positions?: {
    id?: number
    name?: string
    gross?: number
    discount?: number
    [key: string]: any
  }[]
  reversedBy?: null
  reverses?: null
  patient?: number
  payments?: {
    id?: number
    [key: string]: any
  }[]
  practicioner?: number
  settlement?: null
  sentAt?: null
  wahonlinedAt?: null
  diagnoses?: {
    id?: number
    [key: string]: any
  }[]
}

type GetCCPatientCustomfield = {
  id?: number
  values: {
    id?: number
    value?: string
    createdAt?: string
    updatedAt?: string
    createdBy?: number | null
    updatedBy?: number | null
  }[]
  field: GetCCCustomField
  patient?: number | null
}

type PostCCPatientCustomfield = {
  values: {
    id?: number
    value?: string
  }[]
  field: GetCCCustomField
  patient?: number | null
}

type GetInvoiceType = {
  id: number
  invoiceNumber: string
  createdAt: string
  updatedAt: string
  createdBy: number
  updatedBy: number
  addressText: string | null
  discount: number
  status: string
  vatId: string | null
  description: string
  note: string | null
  canceledWhy: string | null
  settlementStatus: string | null
  pdfUrl: string
  invoiceNumberSequence: string
  address: any
  appointment: string | null
  positions: GetInvoicePositionType[]
  reversedBy: string | null
  reverses: string | null
  patient: number
  payments: GetInvoicePaymentType[]
  practicioner: number
  settlement: string | null
  sentAt: string | null
  wahonlinedAt: string | null
  diagnoses: { text: string }[] | null
}

type GetInvoicePaymentType = {
  id: number
  gross: number
  invoice: number
  payment: number
}

type GetInvoicePositionType = {
  id: number
  costCenter: string | null
  invoice: number
  count: number
  originalProduct: string | null
  originalService: 6
  originalDiscount: string | null
  originalGoaeService: string | null
  name: string
  gross: number
  taxRate: number
  code: string | null
  goaeFactor: string | null
  discount: number | null
  discountText: string | null
  additionalText: string
  date: string | null
}

type GetPaymentType = {
  id: number
  paymentNumber: string
  gross: number
  customIdentification?: string
  comment?: string
  createdAt: string
  date: string
  updatedAt: string
  createdBy: number
  updatedBy: number
  register: number
  patient: number
  invoicePayments: GetPaymentInvoicePaymentType[]
  reversedBy?: number
  reverses?: number
  pdfUrl: string
  canceled: boolean
}

type GetPaymentInvoicePaymentType = {
  id: number
  gross: number
  invoice: number
  payment: number
}
