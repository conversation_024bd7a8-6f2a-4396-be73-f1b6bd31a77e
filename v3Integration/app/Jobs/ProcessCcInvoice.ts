import { JobContract } from '@ioc:Rocketseat/Bull'
import { setAPIAuth } from '@/utils/apiAuth'
import { apCustomfield, ccUserReq, contactReq } from '@/request'
import { cLog, reduceCustomFieldValue } from '@/utils'
import { getAPCustomFieldIdByName } from '../helpers/ap'
import Contact from '../Models/Contact'

export default class ProcessCcInvoice implements JobContract {
  public key = 'ProcessCcInvoice'

  public async handle(job) {
    const { payload, auth } = job.data
    await setAPIAuth(auth)
    const contact = await Contact.findBy('ccId', payload.patient)
    contact && contact.apId && (await this.updateCustomfields(contact, payload))
    if (!contact) {
      cLog(`Patient not synced with AP yet. Patient ID: ${payload.patient}`)
    }
  }

  private async updateCustomfields(contact: Contact, data: SocketCCInvoice) {
    const {
      pdfUrl = null,
      discount = 0,
      positions = [],
      practicioner = null,
      diagnoses: Diagnoses,
    } = data

    const diagnoses = Diagnoses || []
    const customFields: { name: string; value: any }[] = []

    if (pdfUrl) {
      customFields.push({ name: 'Latest Invoice PDF URL', value: [pdfUrl] })
    }

    if (positions.length) {
      const totalAmount = positions.reduce((a, b) => a + (b.gross ?? 0), 0)
      const totalDiscount = positions.reduce((a, b) => a + (b.discount ?? 0), 0)
      const productnames: any = []
      positions.filter((ele) => {
        productnames.push(ele.name)
      })
      const diagnosisNames: any = []
      diagnoses.filter((ele) => {
        diagnosisNames.push(ele.text)
      })
      const usersData = await ccUserReq.all()
      let doctorName = ''
      usersData.filter((ele) => {
        if (ele.id === practicioner) {
          doctorName = ele.shortName !== '' ? ele.shortName : ele.firstName + ' ' + ele.lastName
        }
      })
      customFields.push({ name: 'Gross Amount', value: totalAmount })
      customFields.push({ name: 'Discount', value: totalDiscount })
      customFields.push({ name: 'Total Amount', value: totalAmount - totalDiscount - discount })
      customFields.push({ name: 'Latest Payment Status', value: ['Invoice open'] })
      customFields.push({ name: 'Products', value: reduceCustomFieldValue(productnames) })
      customFields.push({ name: 'The Diagnosis', value: reduceCustomFieldValue(diagnosisNames) })
      customFields.push({ name: 'Treated By', value: doctorName })
    }
    if (discount) {
      customFields.push({ name: 'Extra Discount', value: discount })
    }

    if (customFields.length > 0) {
      await (async () => {
        const apCF: { id: string; value: string }[] = []
        const apCustomFields = await apCustomfield.all()
        await Promise.all(
          customFields.map(async (cf) => {
            const id = await getAPCustomFieldIdByName(cf.name, apCustomFields)
            if (id && cf.value && cf.value !== '') {
              apCF.push({
                id: id,
                value: reduceCustomFieldValue(cf.value) + '',
              })
            }
          })
        )
        return apCF
      })().then(async (cf) => {
        await contactReq.update(contact.apId, {
          customFields: cf,
        })
        cLog(`Invoice data has been updated`)
      })
    }
  }
}
