import { JobContract } from '@ioc:Rocketseat/Bull'
import { setAPIAuth } from '@/utils/apiAuth'
import Contact from '../Models/Contact'
import { cLog } from '@/utils'
import { syncInvoice } from '@/app/helpers/ap'

/*
|--------------------------------------------------------------------------
| Job setup
|--------------------------------------------------------------------------
|
| This is the basic setup for creating a job, but you can override
| some settings.
|
| You can get more details by looking at the bullmq documentation.
| https://docs.bullmq.io/
*/
// TODO: Most important
/**
 * We have to calculate credit amount (If a payment don't have invoicePayments, We can count it as credit amount, this one isn't attatched with any invoice {invoicePayments property should be empty})
 *
 * Calculate LTV by
 * filter payments if the reversedBy, reverses is null and cancled is false
 * sum the gross amount (this is LTV)
 *
 * Calculate due amount
 * Get all the open invoice
 * All Invoice amount = invoice positions (gross amounts - position discount) - invoice discount
 * Due amount = All Invoice amount - LTV
 */

export default class ProcessCcPayment implements JobContract {
  public key = 'ProcessCcPayment'

  public async handle(job) {
    const { payload, auth: oAuth } = job.data
    await setAPIAuth(oAuth)
    cLog(`Patient`, payload)
    const contact = await Contact.findBy('ccId', payload.patient)
    //contact && contact.apId && (await this.updateCustomfields(contact, payload))
    if (!contact) {
      cLog(`Patient not synced with AP yet. Patient ID: ${payload.patient}`)
    }
    contact && contact.apId && contact.ccId && (await syncInvoice(contact))
  }

  // private async updateCustomfields(contact: Contact, data: any) {
  //   const { invoicePayments = [], gross = 0, pdfUrl = null, createdAt = null } = data
  //   const customFields: any = []
  //   const auth = getAPIAuth()

  //   if (invoicePayments.length || gross) {
  //     const lastBalanceAmount = await getAPCustomFieldValueByName(contact.apId, 'LTV')
  //     const paymentDone = invoicePayments.length
  //       ? invoicePayments.reduce((a, b) => a + b.gross, 0)
  //       : -gross
  //     const newUpdatedBalance = lastBalanceAmount + paymentDone

  //     customFields.push({ name: 'LTV', values: [newUpdatedBalance] })

  //     const latestPayment = invoicePayments.length
  //       ? invoicePayments[invoicePayments.length - 1].gross
  //       : gross
  //     customFields.push({ name: 'Latest Payment Status', values: ['Success'] })
  //     customFields.push({ name: 'Latest Amount Paid', values: Math.abs(latestPayment) })
  //     customFields.push({
  //       name: 'Latest Payment Date',
  //       values: DateTime.fromISO(createdAt, { zone: 'UTC' })
  //         .setZone(auth.timezone)
  //         .toLocaleString(DateTime.DATE_SHORT),
  //     })
  //     customFields.push({ name: 'Latest Payment PDF URL', values: pdfUrl })
  //   }

  //   if (customFields.length > 0) {
  //     await (async () => {
  //       const apCF: { id: string; value: string }[] = []
  //       const apCustomFields = await apCustomfield.all()
  //       await Promise.all(
  //         customFields.map(async (cf) => {
  //           const id = await getAPCustomFieldIdByName(cf.name, apCustomFields)
  //           cLog(`id: ${id}`)
  //           cLog(`cf.value: ${cf.value}`)
  //           if (id && cf.values && cf.values !== '') {
  //             apCF.push({
  //               id: id,
  //               value: reduceCustomFieldValue(cf.values) + '',
  //             })
  //           }
  //         })
  //       )
  //       return apCF
  //     })().then(async (cf) => {
  //       cLog(`CF`, cf)
  //       await contactReq.update(contact.apId, {
  //         customFields: cf,
  //       })
  //       cLog(`Payment data has been updated`)
  //     })
  //   }
  // }
}
