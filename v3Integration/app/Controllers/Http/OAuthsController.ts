import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import OAuth from 'App/Models/OAuth'
import axios from 'axios'
import { DateTime } from 'luxon'
import qs from 'qs'

export default class OAuthsController {
  public async redirectToProvider(ctx: HttpContextContract) {
    const { response } = ctx
    const scopes = encodeURI(process.env.AP_SCOPE ?? '')
    const url = `https://marketplace.gohighlevel.com/oauth/chooselocation?response_type=code&redirect_uri=${process.env.AP_CALLBACK_URL}&client_id=${process.env.AP_CLIENT_ID}&scope=${scopes}`
    response.redirect(url)
  }

  public processCallback(ctx: HttpContextContract) {
    const { request, response } = ctx
    const code = request.qs().code
    if (code) {
      return OAuthsController.verifyCallback(ctx)
        .then(() => {
          return response.json({
            status: 200,
            message: 'Success',
          })
        })
        .catch((error) => {
          console.log('Error during verification', error.response)
          return response.notAcceptable('Unable to verify.')
        })
    } else console.log('Code not found')
    response.notFound()
  }

  private static async verifyCallback(ctx: HttpContextContract) {
    return new Promise((resolve, reject) => {
      const { request } = ctx
      const code = request.qs().code
      const data = qs.stringify({
        client_id: process.env.AP_CLIENT_ID,
        client_secret: process.env.AP_CLIENT_SECRET,
        grant_type: 'authorization_code',
        code,
      })
      const axiosConfig = {
        method: 'post',
        url: 'https://api.msgsndr.com/oauth/token',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        data,
      }
      return axios(axiosConfig)
        .then((res) => res.data)
        .then(async (res) => {
          OAuthsController.locationDetail(res.locationId)
            .then(async (locationDetail) => {
              const auth = await OAuth.updateOrCreate(
                {
                  location: res.locationId,
                },
                {
                  name: locationDetail.name,
                  timezone: locationDetail.timezone,
                  APStaticToken: locationDetail.apiKey,
                  APAccessToken: res.access_token,
                  APRefreshToken: res.refresh_token,
                  tokenExpire: DateTime.now().plus({ seconds: res.expires_in }),
                }
              )
              return resolve(auth)
            })
            .catch((e) => {
              console.log('Unable to retrieve location details, Error:- ', e.toString())
              return reject(e.toString())
            })
        })
        .catch((e) => {
          console.log('Unable to verify the account, Error:- ', e.toString())
          return reject(e.toString())
        })
    })
  }

  private static async locationDetail(locationId: string) {
    return new Promise<{ [key: string]: string }>((resolve, reject) => {
      const axiosConfig = {
        method: 'get',
        url: 'https://rest.gohighlevel.com/v1/locations/',
        headers: {
          'Authorization': 'Bearer ' + process.env.AP_AGENCY_API,
          'Accept-Encoding': 'application/json',
        },
      }
      return axios(axiosConfig)
        .then((response) => {
          if (response.status === 200) {
            const l = response.data.locations.filter((l) => l.id === locationId)
            if (Object.keys(l).length > 0) {
              return resolve(l[0])
            } else {
              return reject(null)
            }
          } else {
            return reject(null)
          }
        })
        .catch(function (error) {
          console.log('Failed to load Locations', error.response.data)
          reject('Failed to load Locations.')
        })
    })
  }

  public static async refreshAPToken(auth: OAuth) {
    if (auth.tokenExpire < DateTime.now()) {
      const payload = {
        client_id: process.env.AP_CLIENT_ID,
        client_secret: process.env.AP_CLIENT_SECRET,
        grant_type: 'refresh_token',
        refresh_token: auth.APRefreshToken.trim(),
      }
      const query = qs.stringify(payload)
      return axios({
        method: 'post',
        url: 'https://api.msgsndr.com/oauth/token',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        data: query,
      })
        .then((res) => res.data)
        .then(async (res) => {
          const updateAuth = await OAuth.updateOrCreate(
            {
              location: auth.location,
            },
            {
              APAccessToken: res.access_token,
              APRefreshToken: res.refresh_token,
              tokenExpire: DateTime.now().plus({ seconds: res.expires_in }),
            }
          )
          return updateAuth
        })
        .catch((err) => {
          console.log('====================================')
          console.log('Unable to refresh AP token', err.toString())
          console.log('====================================')
          throw new Error(err.toString())
        })
    }
  }
}
