import {
  apAppointmentReq,
  apCustomfield,
  apNoteReq,
  ccAppointmentReq,
  ccLocationReq,
  ccUserReq,
  contactReq,
  invoiceReq,
  patientReq,
  paymentReq,
  resourceReq,
  serviceReq,
} from '@/request'
import Contact from '../Models/Contact'
import { getCCCustomfieldsLabelValue } from './cc'
import { cLog, reduceCustomFieldValue } from '@/utils'
import Appointment from '../Models/Appointment'
import { getPatientContactById } from '@/app/helpers/cc'
import { slackLogger } from '@/utils/slackLogger'
import { removeHtmlTags } from '@/utils/index'
import { DateTime } from 'luxon'
import { getAPIAuth } from '@/utils/apiAuth'
import {
  apLTVCustomfield,
  apPaymentCustomfields,
  apInvoiceCustomfields,
} from '@/utils/apCustomfields'

export const syncCCtoAPCustomfields = async (contact: Contact, extraFields: any = null) => {
  if (!contact.apId || !contact.ccId) {
    throw new Error(
      'AP Contact ID or CC Patient ID is missing, Were not able to sync custom fields'
    )
  }
  let ccNameValue = await getCCCustomfieldsLabelValue(contact)
  if (extraFields) {
    ccNameValue = { ...ccNameValue, ...extraFields }
  }
  ccNameValue['Total Appointments'] = contact.ccData.appointments.length
  ccNameValue['Patient ID'] = contact.ccData.id

  const services: KeyValue[] = await individualServiceAppointmentCount(contact)
  const spends: KeyValue[] = await individualServiceSpends(contact)

  if (Object.keys(services).length > 0) {
    ccNameValue = { ...ccNameValue, ...services }
  }
  if (Object.keys(spends).length > 0) {
    ccNameValue = { ...ccNameValue, ...spends }
  }

  const apNameId = await getApCustomfieldsNameId()
  const payload: PostAPContactType = {
    // tags: ['cc_api'],
    customFields: [],
  }

  if (Object.keys(ccNameValue).length > 0) {
    for (const key in ccNameValue) {
      if (key in apNameId && payload.customFields) {
        payload.customFields.push({
          id: apNameId[key],
          value: ccNameValue[key],
        })
      } else {
        cLog(`No AP Custom Field found with Name ${key}, Creating it now.`)
        const cfRes = await apCustomfield.create({
          name: key,
          dataType: 'TEXT',
        })
        if (cfRes && cfRes.id) {
          payload.customFields?.push({
            id: cfRes.id,
            value: ccNameValue[key],
          })
        }
      }
    }
  }
  if (payload.customFields && payload.customFields.length > 0) {
    const updateRes = await contactReq.update(contact.apId, payload)
    if (updateRes) {
      contact.apData = updateRes
      await contact.save()
      cLog(`CC to AP Customfields has been synced, Contact ID: ${contact.apId}`)
    }
  } else {
    cLog(`No customfields found to sync, Contact ID: ${contact.apId}`)
  }
}

export const getApCustomfieldsNameId = async () => {
  const cf = await apCustomfield.all()
  let nameId = {}
  if (cf.length > 0) {
    cf.map((c) => (nameId[c.name] = c.id))
  }
  return nameId
}

export const syncCCtoAPAppointment = async (appointment: Appointment) => {
  if (appointment.apId) return await updateAppointmentToAP(appointment)
  return await createAppointmentToAP(appointment)
}

export const createAppointmentToAP = async (appointment: Appointment) => {
  if (appointment.apId) return
  const contact = await updateOrCreateContact(
    await getPatientContactById(appointment.patientId),
    false
  )
  const payload: any = {
    contactId: contact.apId,
    startTime: appointment.startAt.toISO() as string,
    endTime: appointment.endAt.toISO() as string,
    appointmentStatus: appointment.ccData.canceledAt ? 'cancelled' : 'confirmed',
  }
  if (appointment.ccData.title) {
    if (appointment.ccData.firstOfPatient) {
      payload.title = `Neukunde: ` + removeHtmlTags(appointment.ccData.title)
    } else {
      payload.title = `Bestandskunde: ` + removeHtmlTags(appointment.ccData.title)
    }
  }
  const apRes = await apAppointmentReq.post(payload)
  if (apRes && apRes.id) {
    appointment.apId = apRes.id
    appointment.apData = apRes
    appointment.contactId = contact.apId
    await appointment.save()
    const tags: string[] = []
    if (appointment.ccData.services && appointment.ccData.services.length > 0) {
      const ccServices = await serviceReq.all()
      ccServices.map((s) => {
        if (appointment.ccData.services.includes(s.id)) {
          tags.push(`Booked ` + s.externalName)
        }
      })
    }

    if (appointment.ccData.firstOfPatient) {
      tags.push('New patient')
    }

    if (tags.length > 0) {
      const apcRes = await contactReq.update(contact.apId, {
        tags: [...tags, ...(contact.apData.tags ?? [])],
      })
      if (apcRes && apcRes.id) {
        contact.apData = apcRes
        await contact.save()
      }
    }
    const noteRes = await apNoteReq.post(
      contact.apId,
      'Appointment Booked: ' + removeHtmlTags(appointment.ccData.title ?? 'No title found')
    )
    if (noteRes && noteRes.id) {
      appointment.apNote = noteRes.id
      await appointment.save()
    }
    cLog(
      `Appointment has been created to AP, ID:- ${appointment.apId}, Will sync custom fields soon.`
    )
    syncAppointmentCustomfieldsToAp(appointment)
  } else {
    cLog(
      `Unable to create appointment to AP, CC Appointment ID: ${appointment.ccId}, Patient ID:- ${appointment.patientId}`
    )
    slackLogger.error(
      `Unable to create appointment to AP, CC Appointment ID: ${appointment.ccId}, Patient ID:- ${appointment.patientId}`
    )
  }
}
export const updateAppointmentToAP = async (appointment: Appointment) => {
  if (!appointment.apId) {
    cLog(
      `Appointment ID is missing, Were not able to update appointment to AP, but creating a new one.`,
      JSON.stringify(appointment)
    )
    return await createAppointmentToAP(appointment)
  }
  const auth = getAPIAuth()
  const payload: any = {
    startTime: appointment.startAt.toISO() as string,
    endTime: appointment.endAt.toISO() as string,
    appointmentStatus: appointment.ccData.canceledAt ? 'cancelled' : 'confirmed',
  }
  if (appointment.ccData.title) {
    if (appointment.ccData.firstOfPatient) {
      payload.title = `Neukunde ` + removeHtmlTags(appointment.ccData.title)
    } else {
      payload.title = `Bestandskunde ` + removeHtmlTags(appointment.ccData.title)
    }
  }
  const apRes = await apAppointmentReq.put(appointment.apId, payload)
  if (apRes && apRes.id) {
    appointment.apId = apRes.id
    appointment.apData = apRes
    await appointment.save()
    cLog(`Appointment has been updated to AP, ID:- ${appointment.apId}.`)
    if (appointment.apNote) {
      const contact = await Contact.findBy('apId', appointment.contactId)
      if (contact?.apId) {
        if (appointment.ccData.canceledAt) {
          await apNoteReq.put(
            contact.apId,
            appointment.apNote,
            `Appointment Canceled: ${removeHtmlTags(
              appointment.ccData.title ?? 'No title found'
            )}\n\nCanceled At: ${DateTime.fromISO(appointment.ccData.canceledAt)
              .setZone(auth.timezone)
              .setLocale('de')
              .toLocaleString(DateTime.DATETIME_FULL)} \n\nCanceled Reason: ${
              appointment.ccData.canceledWhy
            }`
          )
        } else {
          await apNoteReq.put(
            contact.apId,
            appointment.apNote,
            `Appointment Booked: ` + removeHtmlTags(appointment.ccData.title ?? 'No title found')
          )
        }
        cLog(`Note has been update for appointment ${appointment.apId}`)
      }
    } else {
      cLog(`No note found to update for appointment ${appointment.apId}`)
    }
  } else {
    cLog(
      `Unable to update appointment to AP, CC Appointment ID: ${appointment.ccId}, Patient ID:- ${appointment.patientId}`
    )
    slackLogger.error(
      `Unable to update appointment to AP, CC Appointment ID: ${appointment.ccId}, Patient ID:- ${appointment.patientId}`
    )
  }
}

export const updateOrCreateContact = async (
  contact: Contact,
  syncCustomfields = true
): Promise<Contact> => {
  if (!contact.ccData) {
    throw new Error('Required CC data is missing while creating contact')
  }
  if (!contact.email && !contact.phone) {
    throw new Error('Invalid contact data, email and phone is missing.')
  }
  const payload: PostAPContactType = {
    email: contact.email,
    phone: contact.phone,
    firstName: contact.ccData.firstName,
    lastName: contact.ccData.lastName,
    tags: ['cc_api'],
    dateOfBirth: contact.ccData.dob,
  }
  let apContact: GetAPContactType
  if (!contact.apId) {
    payload.source = 'cc'
    payload.gender = contact.ccData.gender
    apContact = await contactReq.upsert(payload)
  } else {
    payload.tags = [...(payload.tags ?? []), ...(contact.apData.tags ?? [])]
    apContact = await contactReq.update(contact.apId, payload)
  }

  if (apContact) {
    contact.apData = apContact
    contact.apId = apContact.id
    await contact.save()
    cLog(`Contact has been synced to ap, ID: ${contact.apId}, Will sync the customfields soon.`)
  } else {
    slackLogger.error(
      `Unable to create/update contact to AP, CC Data: ${JSON.stringify(contact.ccData)}`
    )
    throw new Error('Unable to create/update contact to AP')
  }
  if (syncCustomfields) {
    await syncCCtoAPCustomfields(contact)
  }
  syncInvoicePayments(contact)
  return await contact.refresh()
}

export const syncAppointmentCustomfieldsToAp = async (appointment: Appointment) => {
  const fieldsPayload = []
  fieldsPayload['Last appointment services'] = 'Unknown'
  fieldsPayload['Last appointment treated by'] = 'Unknown'
  fieldsPayload['Last appointment location'] = 'Unknown'
  fieldsPayload['Last appointment resource'] = 'Unknown'
  fieldsPayload['Last appointment categories'] = 'Unknown'

  if (appointment.ccData.services && appointment.ccData.services.length > 0) {
    const services = await serviceReq.all()
    const names: string[] = []
    services.map((service) => {
      if (appointment.ccData.services.includes(service.id)) {
        names.push(service.name)
      }
    })
    fieldsPayload['Last appointment services'] = names.join(', ').replace(/,\s*$/, '')
  }

  if (appointment.ccData.people && appointment.ccData.people.length > 0) {
    const people = await ccUserReq.all()
    const names: string[] = []
    people.map((user) => {
      if (appointment.ccData.people.includes(user.id)) {
        names.push(user.externalName ? user.externalName : user.firstName + ' ' + user.lastName)
      }
    })
    fieldsPayload['Last appointment treated by'] = names.join(', ').replace(/,\s*$/, '')
  }

  if (appointment.ccData.location) {
    const locations = await ccLocationReq.all()
    locations.map((location) => {
      if (appointment.ccData.location === location.id) {
        fieldsPayload['Last appointment location'] = location.name
          ? location.name
          : location.shortName ?? location.id
      }
    })
  }

  if (appointment.ccData.resources && appointment.ccData.resources.length > 0) {
    const resources = await resourceReq.all()
    const names: string[] = []
    resources.map((resource) => {
      if (appointment.ccData.resources.includes(resource.id)) {
        names.push(resource.name)
      }
    })
    fieldsPayload['Last appointment resource'] = names.join(', ').replace(/,\s*$/, '')
  }

  if (appointment.ccData.categories && appointment.ccData.categories.length > 0) {
    const categories = await ccAppointmentReq.category.all()
    const names: string[] = []
    categories.map((category) => {
      if (appointment.ccData.categories.includes(category.id)) {
        names.push(category.title)
      }
    })
    fieldsPayload['Last appointment categories'] = names.join(', ').replace(/,\s*$/, '')
  }
  const contact = await Contact.findBy('ccId', appointment.patientId)
  contact && (await syncCCtoAPCustomfields(contact, fieldsPayload))
}

export const deleteAppointmentFromAP = async (appointment: Appointment) => {
  if (appointment.apId) {
    return await apAppointmentReq.delete(appointment.apId)
  }
  cLog(`Appointment doesn't exist in our database. CC ID: ${appointment.ccId}`)
}

export const getAPCustomFieldValueByName = async (contactId: string, FieldName: string) => {
  const contact = await contactReq.get(contactId)
  const fId = await getAPCustomFieldIdByName(FieldName)
  return contact.customFields && contact.customFields.length > 0 && fId
    ? contact.customFields.find((cf) => cf.id === fId)?.value ?? null
    : null
}
export const getAPCustomFieldIdByName = async (
  name: string,
  customFields: APGetCustomFieldType[] | null = null
) => {
  const fields = customFields ?? (await apCustomfield.all())
  return (
    (fields &&
      fields.find((cf: APGetCustomFieldType) => {
        return cf.name === name
      })?.id) ??
    (await apCustomfield.create({ name, dataType: 'TEXT' }).then((r: any) => r.id))
  )
}

export const updateApCustomfields = async (
  contact: Contact,
  customfields: UpdateAPCustomfields[]
): Promise<Contact> => {
  if (!contact.apId) {
    cLog(`Contact AP Id is missing. CC ID: ${contact.ccId}`)
    return contact
  }
  const apCustomfields = await apCustomfield.all()
  const payload: any = []
  if (customfields.length > 0 && apCustomfields.length > 0) {
    customfields.forEach(async (cf) => {
      const match = apCustomfields.find((apcf) => apcf.name === cf.name)
      if (match) {
        payload.push({ id: match.id, field_value: cf.value })
      } else {
        const create = await apCustomfield.create({ name: cf.name, dataType: 'TEXT' })
        if (create && create.id) {
          payload.push({ id: create.id, field_value: cf.value })
        }
      }
    })
  }
  if (payload.length > 0) {
    const apRes = await contactReq.update(contact.apId, { customFields: payload })
    if (apRes && apRes.id) {
      contact.apData = apRes
      await contact.save()
    }
  }
  return await contact.refresh()
}

export const syncInvoicePayments = async (contact: Contact) => {
  if (!contact.apId || !contact.ccId) {
    cLog(
      `Unable to sync invoice to AP, Contact ID or Patient ID is missing. Contact ID: ${contact.apId}, Patient ID: ${contact.ccId}`
    )
    return contact
  }

  const ccContact = await patientReq.get(contact.ccId)
  if (ccContact && ccContact.id) {
    contact.ccData = ccContact
    await contact.save()
  }
  contact = await contact.refresh()

  contact = await syncInvoice(contact)
  contact = await syncPayment(contact)
}

export const syncInvoice = async (contact: Contact): Promise<Contact> => {
  if (!contact.ccData.invoices || contact.ccData.invoices.length === 0) {
    cLog(`No invoice to sync, Contact ID: ${contact.apId}, Patient ID: ${contact.ccId}`)
    return contact
  }
  const allInvoices = await invoiceReq.get(contact.ccData.invoices)
  if (allInvoices && allInvoices.length > 0) {
    let latestInvoice = allInvoices[0]
    await syncLastInvoice(contact, latestInvoice)
  }
  return contact
}
export const syncPayment = async (contact: Contact): Promise<Contact> => {
  const allPayments = await paymentReq.get(contact.ccData.payments)
  if (allPayments && allPayments.length > 0) {
    await syncLtv(contact, allPayments)
    return await syncLastPayment(contact, allPayments[0])
  }
  cLog(`No payment to sync, Contact ID: ${contact.apId}, Patient ID: ${contact.ccId}`)
  return contact
}
export const syncLtv = async (contact: Contact, payments: GetPaymentType[]): Promise<Contact> => {
  let ltv = 0
  payments.map((payment) => {
    if (
      !payment.canceled &&
      !payment.reversedBy &&
      !payment.reverses &&
      payment.gross > 0 &&
      payment.patient === contact.ccId
    ) {
      ltv += payment.gross
    }
  })
  cLog(`Contact CC ID: ${contact.ccId} -> Ltv: ${ltv}`, { name: apLTVCustomfield, value: ltv })
  return await updateApCustomfields(contact, [{ name: apLTVCustomfield, value: ltv }])
}

export const syncLastInvoice = async (
  contact: Contact,
  invoice: GetInvoiceType
): Promise<Contact> => {
  if (invoice.positions.length > 0) {
    const totalAmount = invoice.positions.reduce((a, b) => a + b.gross, 0)
    const totalDiscount = invoice.positions.reduce((a, b) => (b.discount ? a + b.discount : a), 0)
    const productnames: any = []
    invoice.positions.filter((ele) => {
      productnames.push(ele.name)
    })
    const diagnosisNames: any = []
    invoice.diagnoses &&
      invoice.diagnoses.filter((ele) => {
        diagnosisNames.push(ele.text)
      })
    await updateApCustomfields(contact, [
      { name: apInvoiceCustomfields.LatestInvoicePDFURL, value: invoice.pdfUrl },
      { name: apInvoiceCustomfields.LastInvoiceGrossAmount, value: totalAmount },
      { name: apInvoiceCustomfields.LastInvoiceDiscount, value: totalDiscount },
      {
        name: apInvoiceCustomfields.LastInvoiceTotalAmount,
        value: totalAmount - totalDiscount - invoice.discount,
      },
      { name: apInvoiceCustomfields.LatestPaymentStatus, value: invoice.status },
      {
        name: apInvoiceCustomfields.LastInvoiceProducts,
        value: reduceCustomFieldValue(productnames),
      },
      {
        name: apInvoiceCustomfields.LastInvoiceDiagnosis,
        value: reduceCustomFieldValue(diagnosisNames),
      },
      {
        name: apInvoiceCustomfields.LastInvoiceTreatedBy,
        value: await ccUserReq
          .get(invoice.practicioner)
          ?.then((res) => res.shortName)
          .catch(() => ''),
      },
    ]).then(() => {
      cLog(`Latest invoice updated, Contact ID: ${contact.apId}, Patient ID: ${contact.ccId}`)
    })
  }
  return contact
}
export const syncLastPayment = async (
  contact: Contact,
  payment: GetPaymentType
): Promise<Contact> => {
  const auth = getAPIAuth()
  if (payment.invoicePayments.length || payment.gross) {
    const latestPayment = payment.invoicePayments.length
      ? payment.invoicePayments[payment.invoicePayments.length - 1].gross
      : payment.gross
    await updateApCustomfields(contact, [
      { name: apPaymentCustomfields.LatestPaymentStatus, value: 'Success' },
      { name: apPaymentCustomfields.LatestAmountPaid, value: Math.abs(latestPayment) },
      {
        name: apPaymentCustomfields.LatestPaymentDate,
        value: DateTime.fromISO(payment.createdAt, { zone: 'UTC' })
          .setZone(auth.timezone)
          .toISO() as string,
      },
      { name: apPaymentCustomfields.LatestPaymentPDFURL, value: payment.pdfUrl },
    ]).then(() =>
      cLog(
        `Latest payment has been updated, Contact ID: ${contact.apId}, Patient ID: ${contact.ccId}`
      )
    )
  }
  return contact
}

export const individualServiceAppointmentCount = async (contact: Contact) =>
  new Promise<KeyValue[]>((resolve) => {
    if (contact.ccData && contact.ccData.appointments && contact.ccData.appointments.length < 1) {
      cLog(
        `No appointment to sync individual, Contact ID: ${contact.apId}, Patient ID: ${contact.ccId}`
      )
      return
    }
    ;(async () =>
      new Promise(async (rresolve) => {
        const allAppointments: GetCCAppointmentType[] = []
        for (let index = 0; index < contact.ccData.appointments.length; index++) {
          const id = contact.ccData.appointments[index]
          const appt = await ccAppointmentReq.get(id)
          allAppointments.push(appt)
        }
        rresolve(allAppointments)
      }))().then(async (allAppointments: GetCCAppointmentType[]) => {
      if (allAppointments && allAppointments.length < 1) {
        cLog(`Not found any appointments to sync, Contact ID: ${contact.id}`)
        return
      }
      const services = await serviceReq.all()
      const bookedServices: KeyValue[] = []
      const prefix = 'Total appointments booked for '
      for (let aindex = 0; aindex < allAppointments.length; aindex++) {
        const appointment = allAppointments[aindex]
        if (appointment.services && appointment.services.length > 0) {
          for (let index = 0; index < appointment.services.length; index++) {
            const id = appointment.services[index]
            const service = services.find((s) => s.id === id)
            if (service) {
              bookedServices[prefix + service.name] = bookedServices[prefix + service.name]
                ? bookedServices[prefix + service.name] + 1
                : 1
            }
          }
        }
      }
      cLog(`Individual service appointment count: ${Object.keys(bookedServices).length}`)
      if (Object.keys(bookedServices).length > 0) {
        resolve(bookedServices)
      } else {
        resolve([])
      }
    })
  })
export const individualServiceSpends = async (contact: Contact) =>
  new Promise<KeyValue[]>(async (resolve) => {
    if (contact.ccData && contact.ccData.invoices && contact.ccData.invoices.length < 1) {
      cLog(`No invoice found for contact ${contact.apId}`)
      return resolve([])
    }
    const invoice = await invoiceReq.get(contact.ccData.invoices)
    if (invoice && invoice.length > 0) {
      const prefix = 'Total amount paid for '
      const spends: KeyValue[] = []
      for (let index = 0; index < invoice.length; index++) {
        if (invoice[index].positions && invoice[index].positions.length > 0) {
          for (let aindex = 0; aindex < invoice[index].positions.length; aindex++) {
            const position = invoice[index].positions[aindex]
            if (position.gross) {
              spends[prefix + position.name] = spends[prefix + position.name]
                ? spends[prefix + position.name] + position.gross
                : position.gross
            }
          }
        }
      }
      cLog(`Individual service spends: ${Object.keys(spends).length}`)
      return resolve(spends)
    }
    return resolve([])
  })
