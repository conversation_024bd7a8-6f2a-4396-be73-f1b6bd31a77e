# Field Type Mapping Documentation

This document provides a comprehensive mapping between AutoPatient (AP) and CliniCore (CC) field types for custom field synchronization.

## AutoPatient → CliniCore Field Type Mappings

| AP Field Type | CC Field Type | Multi-Value Support | Notes |
|---------------|---------------|-------------------|-------|
| `TEXT` | `text` | ❌ | Basic text field mapping |
| `LARGE_TEXT` | `textarea` | ❌ | Large text to textarea mapping |
| `NUMERICAL` | `number` | ❌ | Numerical to number mapping |
| `PHONE` | `telephone` | ❌ | Phone to telephone mapping |
| `MONETORY` | `text` | ❌ | Monetary values stored as text in CC |
| `CHECKBOX` | `select` | ✅ | Multi-select checkbox mapping |
| `SINGLE_OPTIONS` | `select` | ❌ | Single select dropdown mapping |
| `MULTIPLE_OPTIONS` | `select` | ✅ | Multi-select dropdown mapping |
| `DATE` | `date` | ❌ | Date field mapping |
| `RADIO` | `select` | ❌ | Radio buttons to single select |
| `RADIO` (Yes/No) | `boolean` | ❌ | Special case for Yes/Ja, No/Nein values |
| `EMAIL` | `email` | ❌ | Email field mapping |
| `TEXTBOX_LIST` | `text` | ✅ | Multi-value text field mapping |
| `FILE_UPLOAD` | ❌ | ❌ | **SKIPPED** - Not supported in CC |

## CliniCore → AutoPatient Field Type Mappings

| CC Field Type | AP Field Type | Multi-Value Handling | Notes |
|---------------|---------------|-------------------|-------|
| `text` | `TEXT` | Single value | Basic text field mapping |
| `text` (multi) | `TEXTBOX_LIST` | ✅ | Multi-value text to TEXTBOX_LIST |
| `textarea` | `LARGE_TEXT` | Single value | Textarea to large text mapping |
| `textarea` (multi) | `TEXTBOX_LIST` | ✅ | Multi-value textarea to TEXTBOX_LIST |
| `number` | `NUMERICAL` | Single value | Number to numerical mapping |
| `number` (multi) | `TEXTBOX_LIST` | ✅ | Multi-value number to TEXTBOX_LIST |
| `telephone` | `PHONE` | Single value | Telephone to phone mapping |
| `telephone` (multi) | `TEXTBOX_LIST` | ✅ | Multi-value telephone to TEXTBOX_LIST |
| `email` | `EMAIL` | Single value | Email field mapping |
| `email` (multi) | `TEXTBOX_LIST` | ✅ | Multi-value email to TEXTBOX_LIST |
| `date` | `DATE` | Single value | Date field mapping |
| `date` (multi) | `TEXTBOX_LIST` | ✅ | Multi-value date to TEXTBOX_LIST |
| `select` | `SINGLE_OPTIONS` | Single value | Single select to dropdown |
| `select` (multi) | `MULTIPLE_OPTIONS` | ✅ | Multi-select to multi-dropdown |
| `select-or-custom` | `SINGLE_OPTIONS` | Single value | Select or custom to dropdown |
| `boolean` | `RADIO` | ❌ | Boolean to Yes/Ja, No/Nein radio |
| `medication` | `TEXT` | ❌ | Medical field to text (fallback) |
| `permanent-diagnoses` | `TEXT` | ❌ | Diagnosis field to text (fallback) |
| `patient-has-recommended` | `TEXT` | ❌ | Recommendation field to text (fallback) |

## Special Field Handling

### TEXTBOX_LIST Fields (AP)
- **Purpose**: Handle multiple values in a single field
- **CC Equivalent**: Any field type with `allowMultipleValues: true`
- **Value Storage**: Uses `field_value` object with option IDs as keys
- **Sync Behavior**: Completely replaces old values (not append)

### Multi-Value Fields (CC)

#### If no matching field found in AP:
- **Identification**: `allowMultipleValues: true`
- **AP Conversion**: Always converts to `TEXTBOX_LIST`
- **Value Handling**: Preserves Record<string, string> structure

#### If found matching field and it's not TEXTBOX_LIST but TEXT:
- **CC → AP**: Join values with ` | ` separator and store in AP TEXT field
- **AP → CC**: Split by ` | ` separator and sync back to CC multi-value field

### Boolean Fields (CC)
- **AP Conversion**: Creates `RADIO` field with options: `["Yes", "Ja", "No", "Nein"]`
- **Value Mapping**: `true` → "Yes", `false` → "No"

### Standard Field Mappings
These are NOT custom fields but standard patient fields:

| AP Standard Field | CC Standard Field | Direction |
|------------------|------------------|-----------|
| `email` | `email` | AP → CC |
| `phone` | `phoneMobile` | AP → CC |
| `firstName` | `firstName` | Bidirectional |
| `lastName` | `lastName` | Bidirectional |
| `dateOfBirth` | `dateOfBirth` | Bidirectional |

### Standard Field to Custom Field Mappings

#### AP Standard Fields → CC Custom Fields
| AP Standard Field | CC Custom Field Name/Label | Purpose | Notes |
|------------------|---------------------------|---------|-------|
| `email` | `email` | Store patient email as custom field | Webhook processing maps this automatically |
| `phone` | `phone`, `phoneMobile`, `phone-mobile` | Store patient phone as custom field | Multiple name variations supported |

#### CC Standard Fields → AP Custom Fields
| CC Standard Field | AP Custom Field Name | Purpose | Notes |
|------------------|---------------------|---------|-------|
| `id` | `PatientID` | Store CC patient ID for reference | Links back to CC patient record |
| `id` | `CC Profile` | Clickable link to CC patient | Custom link: `https://ccdemo.clinicore.eu/patients/{id}/timeline` |

## Email/Phone Custom Field Mapping

### Webhook Processing (WORKS)
During AP webhook processing, email and phone are mapped as custom fields:
- Looks for CC custom fields with `name === "email"` or `label === "email"`
- Looks for CC custom fields with `name === "phone"` or `label === "phone"`
- If found, creates custom field values in CC patient

### Custom Field Sync (ISSUE)
The `syncApToCcCustomFields` function only processes AP custom fields, not standard fields like email/phone.

**Root Cause**: AP email is a standard field, not a custom field, so it's not included in the custom field synchronization process.

## Field Creation Rules

1. **AP → CC**: Never create new CC fields, only map to existing ones
2. **CC → AP**: Create new AP fields if no mapping exists
3. **Conflicts**: Generate unique field names with suffix (e.g., "email_2")
4. **Skipped Fields**: FILE_UPLOAD fields are always skipped

## Value Conversion Notes

- Empty strings are filtered out for email/phone fields
- TEXTBOX_LIST values must respect CC field types during sync
- AllowedValues mismatches are logged and skipped (no API errors)
- Multi-value fields preserve object structure vs comma-separated strings
