# Custom Field Synchronization Rewrite Plan

## 🔥 **PROBLEM STATEMENT**

The current custom field synchronization system in `New/src/processors/customFields/` is fundamentally broken with multiple architectural issues:

1. **Email Sync Failure**: AP standard fields (email/phone) not syncing to CC custom fields
2. **TEXTBOX_LIST Chaos**: Inconsistent Record<string, string> vs comma-separated handling
3. **Missing Pipe Separator**: Multi-value to TEXT conversion doesn't use ` | ` separator
4. **Fake Type Checking**: Field matching always returns `true` without real validation
5. **No Standard Field Architecture**: System only handles custom-to-custom, not standard-to-custom

## 🎯 **SOLUTION: COMPLETE REWRITE**

Build a clean, modular system that handles all field synchronization scenarios properly.

## 📁 **NEW ARCHITECTURE**

```
New/src/processors/customFields/v2/
├── core/
│   ├── fieldMatcher.ts          # Intelligent field matching with real type checking
│   ├── valueConverter.ts        # Unified value conversion with proper separators
│   ├── standardFieldMapper.ts   # Standard field → Custom field mapping
│   └── typeChecker.ts          # Real field type compatibility validation
├── sync/
│   ├── fieldDefinitionSync.ts  # Field creation and mapping sync
│   ├── fieldValueSync.ts       # Patient field value synchronization
│   └── standardFieldSync.ts    # Standard field to custom field sync
├── converters/
│   ├── apToCC.ts               # AP → CC field/value conversion
│   ├── ccToAP.ts               # CC → AP field/value conversion
│   └── textboxList.ts          # TEXTBOX_LIST specialized handling
├── types/
│   └── index.ts                # Clean type definitions
├── config/
│   ├── fieldMappings.ts        # Field type mapping configuration
│   └── standardMappings.ts     # Standard field mapping rules
└── index.ts                    # Main entry point with clean API
```

## 🔧 **CORE COMPONENTS**

### **1. Field Matcher (`core/fieldMatcher.ts`)**
- **Exact matching**: name/label/fieldKey comparison
- **Normalized matching**: Handle German umlauts, spaces, special chars
- **Fuzzy matching**: Similarity-based with configurable threshold
- **Real type compatibility**: Actual validation, not fake `return true`

### **2. Value Converter (`core/valueConverter.ts`)**
- **Unified conversion logic**: Single function for all field types
- **Proper separators**: ` | ` for multi-value → TEXT, `,` for TEXTBOX_LIST
- **Record<string, string> handling**: Consistent object processing
- **Type-aware conversion**: Respect source/target field types

### **3. Standard Field Mapper (`core/standardFieldMapper.ts`)**
- **AP standard → CC custom**: email, phone mapping
- **CC standard → AP custom**: PatientID, CC Profile link
- **Configurable mappings**: Easy to add new standard field mappings
- **Integration with value sync**: Seamless standard field processing

### **4. Type Checker (`core/typeChecker.ts`)**
- **Real compatibility validation**: Based on DATA-TYPE-MAP.md rules
- **Multi-value support detection**: allowMultipleValues handling
- **TEXTBOX_LIST compatibility**: Proper CC field type checking
- **Conversion feasibility**: Can values be converted without loss?
## 🔄 **SYNCHRONIZATION FLOWS**

### **Field Definition Sync** (`sync/fieldDefinitionSync.ts`)
1. Fetch all AP and CC custom fields
2. Match fields using intelligent algorithms
3. Create missing fields (CC → AP only, per rules)
4. Store mappings in database
5. Handle conflicts and duplicates

### **Field Value Sync** (`sync/fieldValueSync.ts`)
1. Fetch patient data from source platform
2. Get field mappings from database
3. Convert values using unified converter
4. Handle TEXTBOX_LIST special cases
5. Update target platform with converted values

### **Standard Field Sync** (`sync/standardFieldSync.ts`)
1. Extract standard fields from patient data
2. Map to target custom fields using configuration
3. Convert values with proper type handling
4. Integrate with regular custom field sync

## 📋 **IMPLEMENTATION PHASES**

### **Phase 1: Core Infrastructure** (Week 1)
- [ ] Set up new directory structure
- [ ] Implement type definitions
- [ ] Build field matcher with real type checking
- [ ] Create unified value converter
- [ ] Add comprehensive unit tests

### **Phase 2: Standard Field Support** (Week 1)
- [ ] Implement standard field mapper
- [ ] Add email/phone → custom field logic
- [ ] Add CC ID → AP custom field logic
- [ ] Test standard field synchronization

### **Phase 3: Synchronization Engines** (Week 2)
- [ ] Build field definition sync
- [ ] Build field value sync
- [ ] Integrate standard field sync
- [ ] Add error handling and logging

### **Phase 4: Integration & Testing** (Week 2)
- [ ] Create feature flag for old/new system
- [ ] Update `/cf` route to use new system
- [ ] Update patient sync endpoints
- [ ] Comprehensive integration testing

### **Phase 5: Migration & Cleanup** (Week 3)
- [ ] Migrate all endpoints to new system
- [ ] Remove old code
- [ ] Update documentation
- [ ] Performance optimization

## 🔀 **MIGRATION STRATEGY**

### **Feature Flag Approach**
```typescript
// Environment variable to control which system to use
const USE_V2_CUSTOM_FIELDS = process.env.USE_V2_CUSTOM_FIELDS === 'true';

export async function syncCustomFields() {
    if (USE_V2_CUSTOM_FIELDS) {
        return await v2.synchronizeCustomFields();
    } else {
        return await v1.synchronizeCustomFields();
    }
}
```

### **Gradual Rollout**
1. **Development**: Test new system thoroughly
2. **Staging**: Run both systems in parallel, compare results
3. **Production**: Feature flag rollout (10% → 50% → 100%)
4. **Cleanup**: Remove old system once stable

## 🎯 **SUCCESS CRITERIA**

### **Functional Requirements**
- [ ] AP email/phone syncs to CC custom fields ✅
- [ ] Multi-value fields use proper ` | ` separator ✅
- [ ] TEXTBOX_LIST handling is consistent ✅
- [ ] Field matching has real type validation ✅
- [ ] Standard field mappings work seamlessly ✅

### **Non-Functional Requirements**
- [ ] 100% test coverage for core components
- [ ] Performance equal or better than old system
- [ ] Zero data loss during migration
- [ ] Comprehensive error handling and logging
- [ ] Clean, maintainable code architecture

## 🚨 **RISK MITIGATION**

### **Data Safety**
- Run old and new systems in parallel during testing
- Comprehensive backup before migration
- Rollback plan with feature flag

### **Performance**
- Benchmark new system against old
- Optimize database queries
- Add caching where appropriate

### **Compatibility**
- Maintain same API interfaces
- Ensure webhook processing still works
- Test all existing integrations

## 📊 **MONITORING & METRICS**

### **Key Metrics to Track**
- Field sync success rate
- Value conversion accuracy
- Standard field mapping success
- Performance (execution time)
- Error rates and types

### **Alerting**
- Failed synchronizations
- Type conversion errors
- Database mapping issues
- Performance degradation

## 🎉 **EXPECTED OUTCOMES**

1. **Email sync works perfectly** - No more brain-fucking issues
2. **Clean, maintainable code** - Easy to understand and extend
3. **Proper error handling** - Clear error messages and recovery
4. **Comprehensive testing** - Confidence in system reliability
5. **Future-proof architecture** - Easy to add new field types and mappings

---

**Next Steps**: Get approval for this plan and start Phase 1 implementation.


