import type { dbSchema } from "@database";
import type { RequestIdVariables } from "hono/request-id";

declare global {
	interface Env {
		Bindings: {
			kv?: KVNamespace;
		} & CloudflareBindings;
		Variables: {
			caches?: {
				patient: import("./src/utils/advancedCache").CloudflareCache;
				appointment: import("./src/utils/advancedCache").CloudflareCache;
				customField: import("./src/utils/advancedCache").CloudflareCache;
				apiResponse: import("./src/utils/advancedCache").CloudflareCache;
			};
		} & RequestIdVariables;
	}
	type TLocalPatient = typeof dbSchema.patient.$inferSelect;
	interface IKeyValue {
		[key: string]: string | number | boolean | null;
	}
}
