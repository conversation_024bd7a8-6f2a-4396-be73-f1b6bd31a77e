const apCustomFields = {
    "customFields": [
        {
            "_id": "RZ63PK5865VER329jxD4",
            "dataType": "STANDARD_FIELD",
            "dateAdded": "2023-01-24T20:58:10.103Z",
            "documentType": "field",
            "fieldKey": "contact.first_name",
            "locationId": "CIY0QcIvP7m9TxVWlvy3",
            "model": "contact",
            "parentId": "8n9ls6fEOWLodF4ZXJFF",
            "position": 0,
            "standard": true,
            "fieldsCount": 0,
            "id": "RZ63PK5865VER329jxD4",
            "name": "First Name"
        },
        {
            "_id": "1N3egIGUsRDeKQwBelR8",
            "dataType": "STANDARD_FIELD",
            "dateAdded": "2023-01-24T20:58:10.104Z",
            "documentType": "field",
            "fieldKey": "contact.type",
            "locationId": "CIY0QcIvP7m9TxVWlvy3",
            "model": "contact",
            "parentId": "8n9ls6fEOWLodF4ZXJFF",
            "position": 300,
            "standard": true,
            "fieldsCount": 0,
            "id": "1N3egIGUsRDeKQwBelR8",
            "name": "Contact Type",
            "picklistOptions": [
                {
                    "value": "lead",
                    "name": "Lead"
                },
                {
                    "value": "customer",
                    "name": "Customer"
                }
            ]
        },
        {
            "_id": "iitHMorKpyy4yU6jA4Wq",
            "allowCustomOption": false,
            "dataType": "TEXT",
            "dateAdded": "2025-07-27T01:15:54.393Z",
            "description": "",
            "documentType": "field",
            "fieldKey": "contact.gender",
            "locationId": "CIY0QcIvP7m9TxVWlvy3",
            "model": "contact",
            "parentId": "8n9ls6fEOWLodF4ZXJFF",
            "placeholder": "",
            "position": 350,
            "showInForms": true,
            "standard": false,
            "fieldsCount": 0,
            "id": "iitHMorKpyy4yU6jA4Wq",
            "name": "Gender"
        },
        {
            "_id": "jyCPCMobLRId4pLUflON",
            "allowCustomOption": false,
            "dataType": "SINGLE_OPTIONS",
            "dateAdded": "2025-07-29T16:13:21.252Z",
            "documentType": "field",
            "fieldKey": "contact.krankenversicherung",
            "locationId": "CIY0QcIvP7m9TxVWlvy3",
            "model": "contact",
            "parentId": "RgQQpmZ5XV9dDm4hUsEX",
            "picklistOptions": [
                "ÖGK",
                "SVS-GW",
                "SVS-LW",
                "BVAEB-OEB",
                "BVAEB-EB",
                "ÖGK-W",
                "ÖGK-N",
                "ÖGK-O",
                "ÖGK-ST",
                "ÖGK-S",
                "ÖGK-V",
                "ÖGK-K",
                "ÖGK-T",
                "ÖGK-B",
                "KFA Wien",
                "PVA",
                "Andere"
            ],
            "placeholder": "",
            "position": 150,
            "standard": false,
            "fieldsCount": 0,
            "id": "jyCPCMobLRId4pLUflON",
            "name": "Krankenversicherung"
        },
        {
            "_id": "xoRhcfgEkKm3ByRIf7jm",
            "allowCustomOption": false,
            "dataType": "RADIO",
            "dateAdded": "2025-07-29T16:13:34.038Z",
            "documentType": "field",
            "fieldKey": "contact.newsletter_erwnscht",
            "locationId": "CIY0QcIvP7m9TxVWlvy3",
            "model": "contact",
            "parentId": "RgQQpmZ5XV9dDm4hUsEX",
            "picklistOptions": [
                "Ja",
                "Nein"
            ],
            "placeholder": "",
            "position": 500,
            "standard": false,
            "fieldsCount": 0,
            "id": "xoRhcfgEkKm3ByRIf7jm",
            "name": "Newsletter erwünscht"
        },
        {
            "_id": "VIZIZE9DHIXcnicGDtNF",
            "allowCustomOption": false,
            "dataType": "LARGE_TEXT",
            "dateAdded": "2025-07-29T16:13:39.517Z",
            "documentType": "field",
            "fieldKey": "contact.notiz",
            "locationId": "CIY0QcIvP7m9TxVWlvy3",
            "model": "contact",
            "parentId": "RgQQpmZ5XV9dDm4hUsEX",
            "placeholder": "",
            "position": 650,
            "standard": false,
            "fieldsCount": 0,
            "id": "VIZIZE9DHIXcnicGDtNF",
            "name": "Notiz"
        },
        {
            "_id": "h9OQmg2yPGSJKfPenVT1",
            "allowCustomOption": false,
            "dataType": "NUMERICAL",
            "dateAdded": "2025-07-29T16:13:44.879Z",
            "documentType": "field",
            "fieldKey": "contact.gewicht_kg",
            "locationId": "CIY0QcIvP7m9TxVWlvy3",
            "model": "contact",
            "parentId": "RgQQpmZ5XV9dDm4hUsEX",
            "placeholder": "",
            "position": 800,
            "standard": false,
            "fieldsCount": 0,
            "id": "h9OQmg2yPGSJKfPenVT1",
            "name": "Gewicht (kg)"
        },
        {
            "_id": "X6QSJaBtazWE2MEyukeS",
            "allowCustomOption": false,
            "dataType": "TEXTBOX_LIST",
            "dateAdded": "2025-07-29T16:13:46.582Z",
            "documentType": "field",
            "fieldKey": "contact.frhere_operationen",
            "locationId": "CIY0QcIvP7m9TxVWlvy3",
            "model": "contact",
            "parentId": "RgQQpmZ5XV9dDm4hUsEX",
            "picklistOptions": [
                {
                    "id": "98dac334-90ec-4e2c-8205-ed3e430e27a3",
                    "label": "Value 1",
                    "prefillValue": ""
                }
            ],
            "placeholder": "",
            "position": 850,
            "showInForms": true,
            "standard": false,
            "fieldsCount": 0,
            "id": "X6QSJaBtazWE2MEyukeS",
            "name": "Frühere Operationen"
        },
        {
            "_id": "bMfVIhpdFXAebgUWCmX4",
            "allowCustomOption": false,
            "dataType": "TEXTBOX_LIST",
            "dateAdded": "2025-07-29T16:13:54.950Z",
            "documentType": "field",
            "fieldKey": "contact.chronische_erkrankungen",
            "locationId": "CIY0QcIvP7m9TxVWlvy3",
            "model": "contact",
            "parentId": "RgQQpmZ5XV9dDm4hUsEX",
            "picklistOptions": [
                {
                    "id": "6b5e90cd-b137-4a4f-87d0-28ddea6b351f",
                    "label": "Value 1",
                    "prefillValue": ""
                },
                {
                    "id": "e3fddf74-50ba-4500-9c61-e68199df48a3",
                    "label": "Value 2",
                    "prefillValue": ""
                }
            ],
            "placeholder": "",
            "position": 1050,
            "showInForms": true,
            "standard": false,
            "fieldsCount": 0,
            "id": "bMfVIhpdFXAebgUWCmX4",
            "name": "Chronische Erkrankungen"
        },
        {
            "_id": "iuXrGBJhfJW2OFDDZYxA",
            "allowCustomOption": false,
            "dataType": "MULTIPLE_OPTIONS",
            "dateAdded": "2025-07-29T16:14:27.572Z",
            "documentType": "field",
            "fieldKey": "contact.allergien_multiselect",
            "locationId": "CIY0QcIvP7m9TxVWlvy3",
            "model": "contact",
            "parentId": "RgQQpmZ5XV9dDm4hUsEX",
            "picklistOptions": [
                "Keine",
                "Novaminsulfon (Metamizol)",
                "Wespe",
                "Sonstige",
                "Penicillin",
                "Ibuprofen (NSAR)",
                "Soja",
                "Latex"
            ],
            "placeholder": "",
            "position": 1900,
            "standard": false,
            "fieldsCount": 0,
            "id": "iuXrGBJhfJW2OFDDZYxA",
            "name": "allergien_multiselect"
        },
        {
            "_id": "yThhunk21kZtuJeXUsxg",
            "allowCustomOption": false,
            "dataType": "DATE",
            "dateAdded": "2025-07-29T16:14:50.035Z",
            "documentType": "field",
            "fieldKey": "contact.datum",
            "locationId": "CIY0QcIvP7m9TxVWlvy3",
            "model": "contact",
            "parentId": "RgQQpmZ5XV9dDm4hUsEX",
            "placeholder": "",
            "position": 2500,
            "standard": false,
            "fieldsCount": 0,
            "id": "yThhunk21kZtuJeXUsxg",
            "name": "Datum"
        },
        {
            "_id": "I5urpAYG6HkQhzV7ccfO",
            "dataType": "STANDARD_FIELD",
            "dateAdded": "2023-06-22T07:30:46.072Z",
            "documentType": "field",
            "fieldKey": "opportunity.name",
            "locationId": "CIY0QcIvP7m9TxVWlvy3",
            "model": "opportunity",
            "parentId": "fzG0NpUtufQLWJEuuy05",
            "position": 0,
            "standard": true,
            "fieldsCount": 0,
            "id": "I5urpAYG6HkQhzV7ccfO",
            "name": "Opportunity Name"
        },
        {
            "_id": "5QxIESWL2o1cYBr9xbVa",
            "dataType": "STANDARD_FIELD",
            "dateAdded": "2023-06-22T07:30:46.072Z",
            "documentType": "field",
            "fieldKey": "opportunity.pipeline_id",
            "locationId": "CIY0QcIvP7m9TxVWlvy3",
            "model": "opportunity",
            "parentId": "fzG0NpUtufQLWJEuuy05",
            "position": 50,
            "standard": true,
            "fieldsCount": 0,
            "id": "5QxIESWL2o1cYBr9xbVa",
            "name": "Pipeline"
        },
        {
            "_id": "jobqO64xBrur1P3KKa4M",
            "dataType": "STANDARD_FIELD",
            "dateAdded": "2023-06-22T07:30:46.072Z",
            "documentType": "field",
            "fieldKey": "opportunity.pipeline_stage_id",
            "locationId": "CIY0QcIvP7m9TxVWlvy3",
            "model": "opportunity",
            "parentId": "fzG0NpUtufQLWJEuuy05",
            "position": 100,
            "standard": true,
            "fieldsCount": 0,
            "id": "jobqO64xBrur1P3KKa4M",
            "name": "Stage"
        },
        {
            "_id": "D2FKTktYJ6ie52dPrQKK",
            "dataType": "STANDARD_FIELD",
            "dateAdded": "2023-06-22T07:30:46.072Z",
            "documentType": "field",
            "fieldKey": "opportunity.status",
            "locationId": "CIY0QcIvP7m9TxVWlvy3",
            "model": "opportunity",
            "parentId": "fzG0NpUtufQLWJEuuy05",
            "position": 150,
            "standard": true,
            "fieldsCount": 0,
            "id": "D2FKTktYJ6ie52dPrQKK",
            "name": "Status"
        },
        {
            "_id": "EPsEjRewzc7fGdbsmfaL",
            "dataType": "STANDARD_FIELD",
            "dateAdded": "2023-06-22T07:30:46.072Z",
            "documentType": "field",
            "fieldKey": "opportunity.monetary_value",
            "locationId": "CIY0QcIvP7m9TxVWlvy3",
            "model": "opportunity",
            "parentId": "fzG0NpUtufQLWJEuuy05",
            "position": 200,
            "standard": true,
            "fieldsCount": 0,
            "id": "EPsEjRewzc7fGdbsmfaL",
            "name": "Lead Value"
        },
        {
            "_id": "S7qAnCW5lCOcfndwzm4Y",
            "dataType": "STANDARD_FIELD",
            "dateAdded": "2023-06-22T07:30:46.072Z",
            "documentType": "field",
            "fieldKey": "opportunity.assigned_to",
            "locationId": "CIY0QcIvP7m9TxVWlvy3",
            "model": "opportunity",
            "parentId": "fzG0NpUtufQLWJEuuy05",
            "position": 250,
            "standard": true,
            "fieldsCount": 0,
            "id": "S7qAnCW5lCOcfndwzm4Y",
            "name": "Opportunity Owner"
        },
        {
            "_id": "ka0tSC61FRinXsalIN7S",
            "dataType": "STANDARD_FIELD",
            "dateAdded": "2023-06-22T07:30:46.072Z",
            "documentType": "field",
            "fieldKey": "opportunity.source",
            "locationId": "CIY0QcIvP7m9TxVWlvy3",
            "model": "opportunity",
            "parentId": "fzG0NpUtufQLWJEuuy05",
            "position": 300,
            "standard": true,
            "fieldsCount": 0,
            "id": "ka0tSC61FRinXsalIN7S",
            "name": "Opportunity Source"
        },
        {
            "_id": "71BY1L8pFs2tUQ4xH6wW",
            "dataType": "STANDARD_FIELD",
            "dateAdded": "2023-12-22T09:00:03.479Z",
            "documentType": "field",
            "fieldKey": "opportunity.lost_reason",
            "locationId": "CIY0QcIvP7m9TxVWlvy3",
            "model": "opportunity",
            "parentId": "fzG0NpUtufQLWJEuuy05",
            "position": 350,
            "standard": true,
            "fieldsCount": 0,
            "id": "71BY1L8pFs2tUQ4xH6wW",
            "name": "Lost Reason"
        },
        {
            "_id": "Z16trJJUKcrNlzXlz4Jp",
            "dataType": "PHONE",
            "dateAdded": "2025-07-22T20:02:00.596Z",
            "documentType": "field",
            "fieldKey": "business.phone",
            "locationId": "CIY0QcIvP7m9TxVWlvy3",
            "model": "business",
            "parentId": "skKydQbtv0aFIeJYQvAZ",
            "position": 2,
            "standard": true,
            "fieldsCount": 0,
            "id": "Z16trJJUKcrNlzXlz4Jp",
            "name": "Phone"
        },
        {
            "_id": "TJGAqGnzqIAliIALX8dA",
            "dataType": "EMAIL",
            "dateAdded": "2025-07-22T20:02:00.980Z",
            "documentType": "field",
            "fieldKey": "business.email",
            "locationId": "CIY0QcIvP7m9TxVWlvy3",
            "model": "business",
            "parentId": "skKydQbtv0aFIeJYQvAZ",
            "position": 3,
            "standard": true,
            "fieldsCount": 0,
            "id": "TJGAqGnzqIAliIALX8dA",
            "name": "Email"
        },
        {
            "_id": "Juy9UsA9sT1F1hlK5W7Q",
            "dataType": "LARGE_TEXT",
            "dateAdded": "2025-07-22T20:02:02.860Z",
            "documentType": "field",
            "fieldKey": "business.description",
            "locationId": "CIY0QcIvP7m9TxVWlvy3",
            "model": "business",
            "parentId": "skKydQbtv0aFIeJYQvAZ",
            "position": 8,
            "standard": true,
            "fieldsCount": 0,
            "id": "Juy9UsA9sT1F1hlK5W7Q",
            "name": "Description"
        },
        {
            "_id": "LuBNNBFBcUPBlnJn3UH4",
            "dataType": "STANDARD_FIELD",
            "dateAdded": "2023-01-24T20:58:10.187Z",
            "documentType": "field",
            "fieldKey": "contact.company_name",
            "locationId": "CIY0QcIvP7m9TxVWlvy3",
            "model": "contact",
            "parentId": "yaZQHuG9JucVKyRIDWBG",
            "position": 0,
            "standard": true,
            "fieldsCount": 0,
            "id": "LuBNNBFBcUPBlnJn3UH4",
            "name": "Business Name"
        },
        {
            "_id": "8NkSP4qwdVbTn5CrOAhg",
            "dataType": "STANDARD_FIELD",
            "dateAdded": "2023-01-24T20:58:10.187Z",
            "documentType": "field",
            "fieldKey": "contact.address1",
            "locationId": "CIY0QcIvP7m9TxVWlvy3",
            "model": "contact",
            "parentId": "yaZQHuG9JucVKyRIDWBG",
            "position": 50,
            "standard": true,
            "fieldsCount": 0,
            "id": "8NkSP4qwdVbTn5CrOAhg",
            "name": "Street Address"
        },
        {
            "_id": "NBk8Rryj7sptjnw5CsQ7",
            "dataType": "STANDARD_FIELD",
            "dateAdded": "2023-01-24T20:58:10.187Z",
            "documentType": "field",
            "fieldKey": "contact.city",
            "locationId": "CIY0QcIvP7m9TxVWlvy3",
            "model": "contact",
            "parentId": "yaZQHuG9JucVKyRIDWBG",
            "position": 100,
            "standard": true,
            "fieldsCount": 0,
            "id": "NBk8Rryj7sptjnw5CsQ7",
            "name": "City"
        },
        {
            "_id": "H2sIqJKZICB0ZcF132dD",
            "dataType": "STANDARD_FIELD",
            "dateAdded": "2023-01-24T20:58:10.187Z",
            "documentType": "field",
            "fieldKey": "contact.country",
            "locationId": "CIY0QcIvP7m9TxVWlvy3",
            "model": "contact",
            "parentId": "yaZQHuG9JucVKyRIDWBG",
            "position": 150,
            "standard": true,
            "fieldsCount": 0,
            "id": "H2sIqJKZICB0ZcF132dD",
            "name": "Country"
        },
        {
            "_id": "iWgV7LH0CMoGhBjeGi9U",
            "dataType": "STANDARD_FIELD",
            "dateAdded": "2023-01-24T20:58:10.187Z",
            "documentType": "field",
            "fieldKey": "contact.state",
            "locationId": "CIY0QcIvP7m9TxVWlvy3",
            "model": "contact",
            "parentId": "yaZQHuG9JucVKyRIDWBG",
            "position": 200,
            "standard": true,
            "fieldsCount": 0,
            "id": "iWgV7LH0CMoGhBjeGi9U",
            "name": "State"
        },
        {
            "_id": "e5hP9gEYbhic6VjNRy3F",
            "dataType": "STANDARD_FIELD",
            "dateAdded": "2023-01-24T20:58:10.187Z",
            "documentType": "field",
            "fieldKey": "contact.postal_code",
            "locationId": "CIY0QcIvP7m9TxVWlvy3",
            "model": "contact",
            "parentId": "yaZQHuG9JucVKyRIDWBG",
            "position": 250,
            "standard": true,
            "fieldsCount": 0,
            "id": "e5hP9gEYbhic6VjNRy3F",
            "name": "Postal Code"
        },
        {
            "_id": "BsZnS7Q8swyXXBDL7P4K",
            "dataType": "STANDARD_FIELD",
            "dateAdded": "2023-01-24T20:58:10.187Z",
            "documentType": "field",
            "fieldKey": "contact.website",
            "locationId": "CIY0QcIvP7m9TxVWlvy3",
            "model": "contact",
            "parentId": "yaZQHuG9JucVKyRIDWBG",
            "position": 300,
            "standard": true,
            "fieldsCount": 0,
            "id": "BsZnS7Q8swyXXBDL7P4K",
            "name": "Website"
        },
        {
            "_id": "12n0jtccCGcJcuuSXf77",
            "dataType": "STANDARD_FIELD",
            "dateAdded": "2023-01-24T20:58:10.187Z",
            "documentType": "field",
            "fieldKey": "contact.timezone",
            "locationId": "CIY0QcIvP7m9TxVWlvy3",
            "model": "contact",
            "parentId": "yaZQHuG9JucVKyRIDWBG",
            "position": 350,
            "standard": true,
            "fieldsCount": 0,
            "id": "12n0jtccCGcJcuuSXf77",
            "name": "Time Zone"
        }
    ],
    "totalItems": 87,
    "traceId": "b7922d81-e7b2-4208-b330-020f83810d58"
}