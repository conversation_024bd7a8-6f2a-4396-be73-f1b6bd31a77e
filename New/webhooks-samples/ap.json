{"Gender": "", "Titel": "Mr.", "Titel (nachgestellt)": "", "Krankenversicherung": "SVS-GW", "Privatversicherung": "", "Zusatzversichert": "false", "Telefon Mobil": {"00513b6f-8c84-42a0-aa42-4d3ab6fe3f04": "+880 1650-483486"}, "Telefon Privat": "", "Telefon Geschäftlich": "", "Terminerinnerungen": "", "Newsletter erwünscht": "", "Familienstand": "ledig", "Beruf": "", "Notiz": "", "Zu uns gekommen durch": "", "Größe (cm)": 360, "Gewicht (kg)": 500, "Frühere Operationen": {"02820f1e-735b-4def-a0db-1f148154a423": "OP 1X", "d9166311-0ae6-415b-924b-65ea42ab6f0f": "OP 2X"}, "Allergien": {"31526d8c-ab75-43eb-b384-e247569f87b1": "X1"}, "Medikation": "", "Dauerdiagnosen": "", "Chronische Erkrankungen": {"dc37a596-baf3-4a0b-ae48-f4466aac7e9b": "Chronische Erkrankungen c", "0c94e209-599d-4b23-8241-77fb45a307e3": "Chronische Erkrankungen b", "2ddbb576-9c76-498b-b351-b8983bac8bc1": "f X", "9cc1df69-dc66-4d05-901d-3f2ddd8673ff": "g Y", "c6fa3cd1-0252-4f3a-ab81-66fdb529a1d5": "h Z"}, "Mitversichert bei (Name)": "", "Mitversichert bei (Nummer)": "", "Augenfarbe": "Blau", "Haarfarbe": "", "Blutdruck": "", "Adresse": "", "Hausarzt": "", "Hausarzt Stammfeld": "", "Befunde": "true", "Empfohlen von": "", "TEST 2 - multiple selection": "", "Allergie": "Gras", "AlexTest": "", "Test3 - Dropdown Multiple": ["3 - Three"], "ExternalId": "", "MultipleValuesTest": "", "allergien_multiselect": "", "Other": "", "Quno External ID": "", "Versichertenkategorie": "", "quno-external-id-1": "", "quno-external-id-2": "", "Quno External Id 3": "", "Patient source": "Doctor", "Patient intake source": "Online", "Testfeld": "", "Test 2025": "", "phoneMobile": "+8801650483486", "Datum": "", "Dropdown mit Freitext (Andere)": "", "Test": "", "allergies_text": "", "quno-external-id-1_text": "", "allergies_text_2": "", "quno-external-id-3_text": "", "testfeld_text": "", "test-2025_text": "", "phonemobile_text": "", "datum_date": "", "title_text": "", "title-suffix_text": "", "private-insurance_text": "", "additionally-insured_boolean": "", "phone-mobile_telephone": "", "phone-personal_telephone": "", "newsletter-wanted_boolean": "", "phone-business_telephone": "", "occupation_text": "", "note_textarea": "", "height_number": "", "weight_number": "", "former-surgeries_text": "", "permanent-diagnoses_permanent-diagnoses": "", "medication_medication": "", "chronic-diseases_text": "", "co-insurance-name_text": "", "co-insurance-number_text": "", "blutdruck_number": "", "adresse_text": "", "hausarzt_text": "", "hausarzt-stammfeld_text": "", "befunde_boolean": "", "empfohlen-von_patient-has-recommended": "", "external-id_text": "", "multiple-values-test_text": "", "other-id_text": "", "quno-external-id_text": "", "contact_id": "KGU5VQTetxTHFAYS5GH9", "first_name": "AP", "last_name": "CC weaa", "full_name": "AP CC weaa", "email": "<EMAIL>", "phone": "+8801650483486", "tags": "", "country": "US", "date_created": "2025-07-30T21:35:04.901Z", "full_address": "", "contact_type": "lead", "location": {"name": "Dhaka", "address": "5915 37th Avenue", "city": "Dhaka", "state": "NY", "country": "US", "postalCode": "11377", "fullAddress": "5915 37th Avenue, Dhaka NY 11377", "id": "CIY0QcIvP7m9TxVWlvy3"}, "workflow": {"id": "ffdf9f52-136e-4872-b072-582f3c267521", "name": "V4 Contact Update"}, "triggerData": {}, "contact": {"attributionSource": {"sessionSource": "CRM UI", "medium": "manual", "mediumId": null}, "lastAttributionSource": {}}, "attributionSource": {}, "customData": {}}