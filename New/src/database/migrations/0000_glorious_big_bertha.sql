CREATE TABLE "alerts" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"alert_type" varchar(50) NOT NULL,
	"severity" varchar(20) NOT NULL,
	"title" varchar(255) NOT NULL,
	"description" text NOT NULL,
	"webhook_id" varchar(255),
	"patient_id" varchar(255),
	"appointment_id" varchar(255),
	"context" jsonb,
	"resolved" integer DEFAULT 0 NOT NULL,
	"resolved_at" timestamp,
	"resolved_by" varchar(255)
);
--> statement-breakpoint
CREATE TABLE "appointments" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"ap_id" varchar(255),
	"cc_id" integer,
	"patient_id" varchar(255),
	"ap_updated_at" timestamp,
	"cc_updated_at" timestamp,
	"ap_data" jsonb,
	"cc_data" jsonb,
	"ap_note_id" text,
	CONSTRAINT "appointments_ap_id_unique" UNIQUE("ap_id"),
	CONSTRAINT "appointments_cc_id_unique" UNIQUE("cc_id")
);
--> statement-breakpoint
CREATE TABLE "custom_fields" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"ap_id" varchar(255),
	"cc_id" integer,
	"name" varchar(255),
	"label" varchar(255),
	"type" varchar(255),
	"ap_config" jsonb,
	"cc_config" jsonb,
	"mapping_type" varchar(50) DEFAULT 'custom_to_custom',
	"ap_standard_field" varchar(255),
	"cc_standard_field" varchar(255),
	CONSTRAINT "custom_fields_ap_id_unique" UNIQUE("ap_id"),
	CONSTRAINT "custom_fields_cc_id_unique" UNIQUE("cc_id")
);
--> statement-breakpoint
CREATE TABLE "duplicate_prevention_locks" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"lock_type" varchar(50) NOT NULL,
	"patient_id" varchar(255),
	"appointment_id" varchar(255),
	"source" varchar(10) NOT NULL,
	"webhook_id" varchar(255) NOT NULL,
	"expires_at" timestamp NOT NULL,
	"context" jsonb
);
--> statement-breakpoint
CREATE TABLE "error_logs" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"request_id" varchar(255),
	"message" text NOT NULL,
	"stack" text,
	"type" varchar(255) NOT NULL,
	"data" jsonb
);
--> statement-breakpoint
CREATE TABLE "patients" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"ap_id" varchar(255),
	"cc_id" integer,
	"email" varchar(255),
	"phone" varchar(255),
	"ap_updated_at" timestamp,
	"cc_updated_at" timestamp,
	"ap_data" jsonb,
	"cc_data" jsonb
);
--> statement-breakpoint
CREATE TABLE "webhook_queue" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"status" varchar(50) DEFAULT 'pending' NOT NULL,
	"priority" integer DEFAULT 100 NOT NULL,
	"source" varchar(10) NOT NULL,
	"entity_type" varchar(50) NOT NULL,
	"entity_id" varchar(255) NOT NULL,
	"patient_id" varchar(255),
	"appointment_id" varchar(255),
	"payload" jsonb NOT NULL,
	"started_at" timestamp,
	"completed_at" timestamp,
	"retry_count" integer DEFAULT 0 NOT NULL,
	"max_retries" integer DEFAULT 3 NOT NULL,
	"next_retry_at" timestamp,
	"error_message" text,
	"processing_time_ms" integer,
	"duplicate_of" varchar(255),
	"duplicate_reason" text
);
--> statement-breakpoint
CREATE TABLE "webhook_queue_logs" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"status" varchar(50) NOT NULL,
	"priority" integer NOT NULL,
	"source" varchar(10) NOT NULL,
	"entity_type" varchar(50) NOT NULL,
	"entity_id" varchar(255) NOT NULL,
	"patient_id" varchar(255),
	"appointment_id" varchar(255),
	"payload" jsonb NOT NULL,
	"started_at" timestamp,
	"completed_at" timestamp,
	"retry_count" integer DEFAULT 0 NOT NULL,
	"error_message" text,
	"processing_time_ms" integer,
	"duplicate_of" varchar(255),
	"duplicate_reason" text
);
--> statement-breakpoint
CREATE TABLE "webhooks" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"request_id" varchar(255),
	"payload" jsonb NOT NULL,
	"source" varchar(64) NOT NULL,
	CONSTRAINT "webhooks_request_id_unique" UNIQUE("request_id")
);
--> statement-breakpoint
ALTER TABLE "appointments" ADD CONSTRAINT "appointments_patient_id_patients_id_fk" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "duplicate_prevention_locks" ADD CONSTRAINT "duplicate_prevention_locks_patient_id_patients_id_fk" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "duplicate_prevention_locks" ADD CONSTRAINT "duplicate_prevention_locks_appointment_id_appointments_id_fk" FOREIGN KEY ("appointment_id") REFERENCES "public"."appointments"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "webhook_queue" ADD CONSTRAINT "webhook_queue_patient_id_patients_id_fk" FOREIGN KEY ("patient_id") REFERENCES "public"."patients"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "webhook_queue" ADD CONSTRAINT "webhook_queue_appointment_id_appointments_id_fk" FOREIGN KEY ("appointment_id") REFERENCES "public"."appointments"("id") ON DELETE no action ON UPDATE no action;