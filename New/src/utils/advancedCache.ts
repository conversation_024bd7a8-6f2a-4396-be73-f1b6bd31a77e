/**
 * Advanced Caching Utility for Cloudflare Workers
 *
 * Provides intelligent in-memory caching with TTL, LRU eviction, and pattern-based
 * cache invalidation optimized for API responses in Cloudflare Workers environment.
 *
 * Features:
 * - TTL-based cache expiration
 * - LRU eviction when cache size limit is reached
 * - Pattern-based cache invalidation for related endpoints
 * - Memory efficient for Workers runtime
 * - Thread-safe for concurrent requests
 */

/**
 * Cache entry interface
 */
interface CacheEntry<T> {
	data: T;
	timestamp: number;
	ttl: number;
	accessCount: number;
	lastAccessed: number;
}

/**
 * Cache configuration options
 */
interface CacheOptions {
	/** Time to live in milliseconds (default: 5 minutes) */
	ttl?: number;
	/** Maximum number of cache entries (default: 100) */
	maxSize?: number;
	/** Enable automatic cleanup of expired entries (default: true) */
	autoCleanup?: boolean;
}

/**
 * Cloudflare Workers-compatible cache implementation
 */
export class CloudflareCache<T = unknown> {
	private cache = new Map<string, CacheEntry<T>>();
	private readonly defaultTTL: number;
	private readonly maxSize: number;
	private readonly autoCleanup: boolean;

	constructor(options: CacheOptions = {}) {
		this.defaultTTL = options.ttl ?? 5 * 60 * 1000; // 5 minutes
		this.maxSize = options.maxSize ?? 100;
		this.autoCleanup = options.autoCleanup ?? true;
	}

	/**
	 * Generate a cache key from request parameters
	 */
	generateKey(
		method: string,
		url: string,
		params?: Record<string, unknown>,
	): string {
		const paramsStr = params ? JSON.stringify(params) : "{}";
		return `${method}:${url}:${paramsStr}`;
	}

	/**
	 * Get cached data if available and not expired
	 */
	get(key: string): T | undefined {
		if (this.autoCleanup) {
			this.cleanupExpired();
		}

		const entry = this.cache.get(key);
		if (!entry) {
			return undefined;
		}

		// Check if entry has expired
		const now = Date.now();
		if (now - entry.timestamp > entry.ttl) {
			this.cache.delete(key);
			return undefined;
		}

		// Update access statistics for LRU
		entry.accessCount++;
		entry.lastAccessed = now;

		return entry.data;
	}

	/**
	 * Store data in cache with TTL
	 */
	set(key: string, data: T, ttl?: number): void {
		const now = Date.now();
		const entryTTL = ttl ?? this.defaultTTL;

		// Ensure cache size limit
		if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
			this.evictLRU();
		}

		const entry: CacheEntry<T> = {
			data,
			timestamp: now,
			ttl: entryTTL,
			accessCount: 1,
			lastAccessed: now,
		};

		this.cache.set(key, entry);
	}

	/**
	 * Delete a specific cache entry
	 */
	delete(key: string): boolean {
		return this.cache.delete(key);
	}

	/**
	 * Clear all cache entries
	 */
	clear(): void {
		this.cache.clear();
	}

	/**
	 * Invalidate cache entries based on URL patterns
	 * Supports both exact matches and parent resource invalidation
	 */
	invalidatePattern(url: string): number {
		let invalidatedCount = 0;
		const keysToDelete: string[] = [];

		// Extract base path for pattern matching
		const basePath = this.extractBasePath(url);
		const patterns = [url, basePath].filter(Boolean);

		for (const key of this.cache.keys()) {
			const keyUrl = this.extractUrlFromKey(key);
			if (
				keyUrl &&
				patterns.some((pattern) => this.matchesPattern(keyUrl, pattern))
			) {
				keysToDelete.push(key);
			}
		}

		// Delete matched entries
		for (const key of keysToDelete) {
			this.cache.delete(key);
			invalidatedCount++;
		}

		return invalidatedCount;
	}

	/**
	 * Get cache statistics
	 */
	getStats(): {
		size: number;
		maxSize: number;
		hitRate: number;
		oldestEntry: number | null;
	} {
		let oldestTimestamp: number | null = null;
		let totalAccess = 0;
		let totalEntries = 0;

		for (const entry of this.cache.values()) {
			totalAccess += entry.accessCount;
			totalEntries++;
			if (oldestTimestamp === null || entry.timestamp < oldestTimestamp) {
				oldestTimestamp = entry.timestamp;
			}
		}

		return {
			size: this.cache.size,
			maxSize: this.maxSize,
			hitRate: totalEntries > 0 ? totalAccess / totalEntries : 0,
			oldestEntry: oldestTimestamp,
		};
	}

	/**
	 * Clean up expired cache entries
	 */
	private cleanupExpired(): void {
		const now = Date.now();
		const keysToDelete: string[] = [];

		for (const [key, entry] of this.cache.entries()) {
			if (now - entry.timestamp > entry.ttl) {
				keysToDelete.push(key);
			}
		}

		for (const key of keysToDelete) {
			this.cache.delete(key);
		}
	}

	/**
	 * Evict least recently used entry to make space
	 */
	private evictLRU(): void {
		let lruKey: string | null = null;
		let lruScore = Number.POSITIVE_INFINITY;

		for (const [key, entry] of this.cache.entries()) {
			// LRU score based on last access time and access count
			const score = entry.lastAccessed - entry.accessCount * 1000;
			if (score < lruScore) {
				lruScore = score;
				lruKey = key;
			}
		}

		if (lruKey) {
			this.cache.delete(lruKey);
		}
	}

	/**
	 * Extract base path from URL for pattern matching
	 * Example: "/contacts/123" -> "/contacts/"
	 */
	private extractBasePath(url: string): string {
		const parts = url.split("/").filter(Boolean);
		if (parts.length <= 1) {
			return url;
		}

		// Remove the last part if it looks like an ID (numeric or UUID-like)
		const lastPart = parts[parts.length - 1];
		if (
			/^[\d\w-]+$/.test(lastPart) &&
			(/^\d+$/.test(lastPart) || // numeric ID
				/^[a-f0-9-]{8,}$/i.test(lastPart)) // UUID-like
		) {
			parts.pop();
		}

		return `/${parts.join("/")}/`;
	}

	/**
	 * Extract URL from cache key
	 */
	private extractUrlFromKey(key: string): string | null {
		const parts = key.split(":");
		return parts.length >= 2 ? parts[1] : null;
	}

	/**
	 * Check if URL matches invalidation pattern
	 */
	private matchesPattern(url: string, pattern: string): boolean {
		// Exact match
		if (url === pattern) {
			return true;
		}

		// Parent resource match (e.g., "/contacts/" matches "/contacts/123")
		if (pattern.endsWith("/") && url.startsWith(pattern)) {
			return true;
		}

		// Child resource match (e.g., "/contacts/123" matches "/contacts/")
		if (url.endsWith("/") && pattern.startsWith(url)) {
			return true;
		}

		return false;
	}
}

/**
 * Global cache instances for different data types
 */
export const apiResponseCache = new CloudflareCache<unknown>({
	ttl: 5 * 60 * 1000, // 5 minutes
	maxSize: 100,
});

export const patientCache = new CloudflareCache<unknown>({
	ttl: 10 * 60 * 1000, // 10 minutes for patient data
	maxSize: 50,
});

export const appointmentCache = new CloudflareCache<unknown>({
	ttl: 3 * 60 * 1000, // 3 minutes for appointment data
	maxSize: 50,
});

export const customFieldCache = new CloudflareCache<unknown>({
	ttl: 15 * 60 * 1000, // 15 minutes for custom fields (rarely change)
	maxSize: 30,
});

/**
 * Default export for general API response caching
 */
export default apiResponseCache;
