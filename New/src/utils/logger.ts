/**
 * Centralized Logging Utility for DermaCare DataSync
 *
 * Provides standardized logging functions with consistent formatting,
 * request ID tracking, ISO timestamps, and log level filtering.
 *
 * Features:
 * - Consistent log format: [requestId] [timestamp] message
 * - Log level filtering (DEBUG, INFO, WARN, ERROR)
 * - Production mode support with reduced verbosity
 * - Request context propagation
 * - Performance-optimized for production environments
 */

import { getConfig, type LoggingConfig, type LogLevel } from "@config";
import { getRequestId } from "@/utils/getRequestId";

/**
 * Log level hierarchy for filtering
 */
const LOG_LEVELS: Record<LogLevel, number> = {
	DEBUG: 0,
	INFO: 1,
	WARN: 2,
	ERROR: 3,
};

/**
 * Check if a log level should be output based on current configuration
 */
const shouldLog = (level: LogLevel): boolean => {
	const config = getConfig("logging") as LoggingConfig;
	const currentLevelValue = LOG_LEVELS[config.level];
	const messageLevelValue = LOG_LEVELS[level];
	return messageLevelValue >= currentLevelValue;
};

/**
 * Format log message with consistent structure
 */
const formatLogMessage = (
	requestId: string,
	level: LogLevel,
	message: string,
	data?: unknown,
): string => {
	const timestamp = new Date().toISOString();
	const baseMessage = `[${requestId}] [${timestamp}] [${level}] ${message}`;

	if (data !== undefined) {
		const config = getConfig("logging") as LoggingConfig;
		if (config.includeDebugInfo) {
			return `${baseMessage}\nData: ${JSON.stringify(data, null, 2)}`;
		} else {
			// In production, limit data output to prevent log spam
			const dataStr =
				typeof data === "object" && data !== null
					? `${JSON.stringify(data).substring(0, 200)}...`
					: String(data).substring(0, 200);
			return `${baseMessage} | Data: ${dataStr}`;
		}
	}

	return baseMessage;
};

/**
 * Log debug messages (lowest priority)
 * Only shown when LOG_LEVEL is DEBUG
 * Automatically retrieves request ID from context
 */
export const logDebug = (message: string, data?: unknown): void => {
	if (shouldLog("DEBUG")) {
		const requestId = getRequestId();
		console.log(formatLogMessage(requestId, "DEBUG", message, data));
	}
};

/**
 * Log informational messages
 * Shown when LOG_LEVEL is DEBUG or INFO
 * Automatically retrieves request ID from context
 */
export const logInfo = (message: string, data?: unknown): void => {
	if (shouldLog("INFO")) {
		const requestId = getRequestId();
		console.log(formatLogMessage(requestId, "INFO", message, data));
	}
};

/**
 * Log warning messages
 * Shown when LOG_LEVEL is DEBUG, INFO, or WARN
 * Automatically retrieves request ID from context
 */
export const logWarn = (message: string, data?: unknown): void => {
	if (shouldLog("WARN")) {
		const requestId = getRequestId();
		console.warn(formatLogMessage(requestId, "WARN", message, data));
	}
};

/**
 * Log error messages (highest priority)
 * Always shown regardless of log level
 * Automatically retrieves request ID from context
 */
export const logError = (message: string, error?: unknown): void => {
	if (shouldLog("ERROR")) {
		const requestId = getRequestId();
		const errorData =
			error instanceof Error
				? { message: error.message, stack: error.stack }
				: error;
		console.error(formatLogMessage(requestId, "ERROR", message, errorData));
	}
};

/**
 * Log API performance metrics
 * Special formatting for API call performance tracking
 * Automatically retrieves request ID from context
 */
export const logApiPerformance = (
	method: string,
	status: number,
	duration: number,
	url: string,
): void => {
	if (shouldLog("INFO")) {
		const requestId = getRequestId();
		const message = `API Call: [${method}] [${status}] -> ${duration.toFixed(2)}s -> ${url}`;
		console.log(formatLogMessage(requestId, "INFO", message));
	}
};

/**
 * Log database operation performance
 * Special formatting for database operation tracking
 * Automatically retrieves request ID from context
 */
export const logDbPerformance = (
	operation: string,
	duration: number,
	details?: string,
): void => {
	if (shouldLog("INFO")) {
		const requestId = getRequestId();
		const message = `DB Operation: ${operation} -> ${duration.toFixed(2)}ms${details ? ` -> ${details}` : ""}`;
		console.log(formatLogMessage(requestId, "INFO", message));
	}
};

/**
 * Log processing step completion
 * Used for tracking major processing milestones
 * Automatically retrieves request ID from context
 */
export const logProcessingStep = (
	step: string,
	duration?: number,
	details?: unknown,
): void => {
	if (shouldLog("INFO")) {
		const requestId = getRequestId();
		const durationStr = duration ? ` (${duration}ms)` : "";
		const message = `Processing: ${step}${durationStr}`;
		console.log(formatLogMessage(requestId, "INFO", message, details));
	}
};

/**
 * Log custom field operations
 * Specialized logging for custom field processing
 * Automatically retrieves request ID from context
 */
export const logCustomField = (
	operation: string,
	fieldName: string,
	details?: unknown,
): void => {
	if (shouldLog("DEBUG")) {
		const requestId = getRequestId();
		const message = `Custom Field ${operation}: ${fieldName}`;
		console.log(formatLogMessage(requestId, "DEBUG", message, details));
	}
};

/**
 * Log webhook events
 * Specialized logging for webhook processing
 * Automatically retrieves request ID from context
 */
export const logWebhook = (
	event: string,
	model: string,
	entityId?: string | number,
): void => {
	if (shouldLog("INFO")) {
		const requestId = getRequestId();
		const message = `Webhook: ${event} -> ${model}${entityId ? ` -> ID: ${entityId}` : ""}`;
		console.log(formatLogMessage(requestId, "INFO", message));
	}
};

/**
 * Enhanced field type conversion logging
 * Provides comprehensive debugging information for field type conversion failures
 *
 * @param conversionDirection - Direction of conversion (e.g., "AP→CC", "CC→AP")
 * @param sourceField - Source field details
 * @param targetField - Target field details
 * @param sourceValue - Value being converted (truncated if necessary)
 * @param error - Conversion error details
 * @param requestId - Request ID for correlation (optional, will auto-detect if not provided)
 */
export const logFieldConversionFailure = (
	conversionDirection: string,
	sourceField: {
		id: string | number;
		name: string;
		type: string;
		fieldKey?: string;
		picklistOptions?: string[];
		allowMultipleValues?: boolean;
		allowedValues?: Array<{ id?: number; value: string }>;
	},
	targetField: {
		id: string | number;
		name: string;
		type: string;
		fieldKey?: string;
		picklistOptions?: string[];
		allowMultipleValues?: boolean;
		allowedValues?: Array<{ id?: number; value: string }>;
	},
	sourceValue: unknown,
	error: string,
): void => {
	if (shouldLog("WARN")) {
		const actualRequestId = getRequestId();

		// Truncate value for readability while preserving type information
		const truncatedValue = truncateValue(sourceValue);

		const logData = {
			requestId: actualRequestId,
			conversionDirection,
			error,
			sourceField: {
				id: sourceField.id,
				name: sourceField.name,
				type: sourceField.type,
				...(sourceField.fieldKey && { fieldKey: sourceField.fieldKey }),
				...(sourceField.picklistOptions && {
					picklistOptions: sourceField.picklistOptions.slice(0, 5), // Show first 5 options
					totalOptions: sourceField.picklistOptions.length,
				}),
				...(sourceField.allowMultipleValues !== undefined && {
					allowMultipleValues: sourceField.allowMultipleValues,
				}),
				...(sourceField.allowedValues && {
					allowedValues: sourceField.allowedValues
						.slice(0, 5)
						.map((v) => v.value), // Show first 5 values
					totalAllowedValues: sourceField.allowedValues.length,
				}),
			},
			targetField: {
				id: targetField.id,
				name: targetField.name,
				type: targetField.type,
				...(targetField.fieldKey && { fieldKey: targetField.fieldKey }),
				...(targetField.picklistOptions && {
					picklistOptions: targetField.picklistOptions.slice(0, 5),
					totalOptions: targetField.picklistOptions.length,
				}),
				...(targetField.allowMultipleValues !== undefined && {
					allowMultipleValues: targetField.allowMultipleValues,
				}),
				...(targetField.allowedValues && {
					allowedValues: targetField.allowedValues
						.slice(0, 5)
						.map((v) => v.value),
					totalAllowedValues: targetField.allowedValues.length,
				}),
			},
			sourceValue: truncatedValue,
			conversionContext: {
				sourceFieldType: sourceField.type,
				targetFieldType: targetField.type,
				expectedConversion: `${sourceField.type} → ${targetField.type}`,
				supportedConversions: getSupportedConversions(conversionDirection),
			},
		};

		console.warn(
			formatLogMessage(
				actualRequestId,
				"WARN",
				`Field conversion failure: ${error}`,
				logData,
			),
		);
	}
};

/**
 * Enhanced CC API error logging
 * Provides detailed information about CC API failures including request/response data
 *
 * @param operation - API operation description
 * @param error - Error object or message
 * @param requestData - Request payload that caused the error
 * @param responseData - Response data from the API (if available)
 */
export const logCcApiError = (
	operation: string,
	error: unknown,
	requestData?: unknown,
	responseData?: unknown,
): void => {
	if (shouldLog("ERROR")) {
		const actualRequestId = getRequestId();

		// Extract error details
		const errorDetails = extractErrorDetails(error);

		const logData: Record<string, unknown> = {
			requestId: actualRequestId,
			operation,
			error: errorDetails,
			timestamp: new Date().toISOString(),
		};

		if (requestData) {
			logData.requestData = truncateValue(requestData, 1000);
		}

		if (responseData) {
			logData.responseData = truncateValue(responseData, 1000);
		}

		console.error(
			formatLogMessage(
				actualRequestId,
				"ERROR",
				`CC API Error: ${operation}`,
				logData,
			),
		);
	}
};

/**
 * Truncate value for logging while preserving type information
 */
function truncateValue(value: unknown, maxLength: number = 200): unknown {
	if (value === null || value === undefined) {
		return value;
	}

	if (typeof value === "string") {
		return value.length > maxLength
			? `${value.substring(0, maxLength)}... [truncated, total length: ${value.length}]`
			: value;
	}

	if (Array.isArray(value)) {
		if (value.length === 0) return value;
		const truncated = value.slice(0, 3); // Show first 3 items
		return value.length > 3
			? [...truncated, `... [${value.length - 3} more items]`]
			: value;
	}

	if (typeof value === "object") {
		const stringified = JSON.stringify(value);
		return stringified.length > maxLength
			? `${stringified.substring(0, maxLength)}... [truncated, total length: ${stringified.length}]`
			: value;
	}

	return value;
}

/**
 * Extract detailed error information from various error types
 */
function extractErrorDetails(error: unknown): Record<string, unknown> {
	if (error instanceof Error) {
		const details: Record<string, unknown> = {
			message: error.message,
			name: error.name,
		};

		if (error.stack) {
			details.stack = error.stack.split("\n").slice(0, 5).join("\n"); // First 5 lines of stack
		}

		// Check for enhanced error with details property
		if ("details" in error && typeof error.details === "object") {
			details.apiDetails = error.details;
		}

		return details;
	}

	if (typeof error === "string") {
		return { message: error };
	}

	if (typeof error === "object" && error !== null) {
		return { details: error };
	}

	return { message: String(error) };
}

/**
 * Get supported conversions for a given direction
 */
function getSupportedConversions(direction: string): string[] {
	if (direction === "AP→CC") {
		return [
			"RADIO → boolean (2 options)",
			"RADIO → select (multiple options)",
			"MULTIPLE_OPTIONS → select (allowMultipleValues: true)",
			"SINGLE_OPTIONS → select (allowMultipleValues: false)",
			"TEXT → text/email/telephone",
			"TEXT → select (when value matches allowed options)",
			"NUMERICAL → number",
			"LARGE_TEXT → textarea",
		];
	}

	if (direction === "CC→AP") {
		return [
			"boolean → RADIO (Yes/No)",
			"select (single) → RADIO/SINGLE_OPTIONS",
			"select (multiple) → MULTIPLE_OPTIONS",
			"text/email/telephone → TEXT",
			"number → NUMERICAL",
			"textarea → LARGE_TEXT",
			"select-or-custom → SINGLE_OPTIONS",
		];
	}

	return [];
}

/**
 * Legacy console.log replacement
 * Provides backward compatibility while encouraging migration to structured logging
 * @deprecated Use specific log functions (logInfo, logError, etc.) instead
 */
export const log = (message: string, data?: unknown): void => {
	logInfo(message, data);
};

/**
 * Get current logging configuration
 * Useful for debugging logging issues
 */
export const getLoggingConfig = (): LoggingConfig => {
	return getConfig("logging") as LoggingConfig;
};

/**
 * Check if debug logging is enabled
 * Useful for conditional expensive debug operations
 */
export const isDebugEnabled = (): boolean => {
	return shouldLog("DEBUG");
};

/**
 * Check if production mode is enabled
 * Useful for conditional production optimizations
 */
export const isProductionMode = (): boolean => {
	return (getConfig("logging") as LoggingConfig).isProduction;
};
