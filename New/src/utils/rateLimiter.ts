/**
 * Rate Limiting Utility
 *
 * Provides rate limiting functionality for the /cf endpoint with database-backed
 * tracking and comprehensive logging. Implements a sliding window approach with
 * 4 requests per 24-hour period limit specifically for custom field synchronization.
 *
 * Features:
 * - Database-backed request tracking using webhooks table
 * - Sliding 24-hour window rate limiting
 * - Comprehensive logging with request ID correlation
 * - Detailed rate limit information for client responses
 * - Automatic cleanup of old rate limit records
 *
 * @fileoverview Rate limiting utility for /cf endpoint
 * @version 1.0.0
 * @since 2024-07-27
 */

import { dbSchema, getDb } from "@database";
import { and, desc, gte, like, lt } from "drizzle-orm";
import type { RateLimitResult } from "@/processors/patientCustomFields/types";
import { logDebug, logInfo, logWarn } from "@/utils/logger";
import { getRequestId } from "./getRequestId";

/**
 * Rate limiting configuration constants
 */
const RATE_LIMIT_CONFIG = {
	/** Maximum number of requests allowed per time window */
	MAX_REQUESTS: 4,
	/** Time window in milliseconds (24 hours) */
	WINDOW_MS: 24 * 60 * 60 * 1000,
	/** Source identifier for rate limit tracking (using 'cc' as allowed enum value) */
	SOURCE: "cc" as const,
} as const;

/**
 * Check rate limit for /cf endpoint
 *
 * Verifies if the current request is within the rate limit of 4 calls per 24 hours.
 * Uses the webhooks table to track requests with a sliding window approach.
 * Automatically cleans up old rate limit records to prevent database bloat.
 *
 * @returns Promise resolving to rate limit check result with detailed information
 *
 * @example
 * ```typescript
 * const rateLimitResult = await checkCfRateLimit("req-123");
 *
 * if (!rateLimitResult.allowed) {
 *   return c.json({
 *     error: "Rate limit exceeded",
 *     requestCount: rateLimitResult.requestCount,
 *     maxRequests: rateLimitResult.maxRequests,
 *     resetInSeconds: rateLimitResult.resetInSeconds
 *   }, 429);
 * }
 * ```
 *
 * @since 1.0.0
 */
export async function checkCfRateLimit(): Promise<RateLimitResult> {
	const traceId = getRequestId();
	const db = getDb();
	const now = new Date();
	const windowStart = new Date(now.getTime() - RATE_LIMIT_CONFIG.WINDOW_MS);

	logDebug("Checking /cf endpoint rate limit", {
		traceId,
		windowStart: windowStart.toISOString(),
		currentTime: now.toISOString(),
		maxRequests: RATE_LIMIT_CONFIG.MAX_REQUESTS,
	});

	try {
		// Get recent requests within the 24-hour window
		const recentRequests = await db
			.select({
				id: dbSchema.webhooks.id,
				createdAt: dbSchema.webhooks.createdAt,
				requestId: dbSchema.webhooks.requestId,
			})
			.from(dbSchema.webhooks)
			.where(
				and(
					gte(dbSchema.webhooks.createdAt, windowStart),
					// Filter by request ID pattern for rate limit tracking
					like(dbSchema.webhooks.requestId, "cf-rate-limit-%"),
				),
			)
			.orderBy(desc(dbSchema.webhooks.createdAt));

		const requestCount = recentRequests.length;
		const allowed = requestCount < RATE_LIMIT_CONFIG.MAX_REQUESTS;

		// Calculate reset time (24 hours from the oldest request in window)
		const oldestRequest = recentRequests[recentRequests.length - 1];
		const resetTime = oldestRequest
			? new Date(
					oldestRequest.createdAt.getTime() + RATE_LIMIT_CONFIG.WINDOW_MS,
				)
			: now;
		const resetInSeconds = Math.max(
			0,
			Math.ceil((resetTime.getTime() - now.getTime()) / 1000),
		);

		const result: RateLimitResult = {
			allowed,
			requestCount,
			maxRequests: RATE_LIMIT_CONFIG.MAX_REQUESTS,
			remainingRequests: Math.max(
				0,
				RATE_LIMIT_CONFIG.MAX_REQUESTS - requestCount,
			),
			resetTime,
			resetInSeconds,
		};

		if (allowed) {
			logDebug("Rate limit check passed", {
				requestCount,
				maxRequests: RATE_LIMIT_CONFIG.MAX_REQUESTS,
				remainingRequests: RATE_LIMIT_CONFIG.MAX_REQUESTS - requestCount,
			});
		} else {
			logWarn("Rate limit exceeded for /cf endpoint", {
				requestCount,
				maxRequests: RATE_LIMIT_CONFIG.MAX_REQUESTS,
				resetInSeconds,
				resetTime: resetTime.toISOString(),
			});
		}

		return result;
	} catch (error) {
		// If rate limit check fails, allow the request but log the error
		logWarn("Rate limit check failed, allowing request", {
			error: String(error),
		});

		return {
			allowed: true,
			requestCount: 0,
			maxRequests: RATE_LIMIT_CONFIG.MAX_REQUESTS,
			remainingRequests: RATE_LIMIT_CONFIG.MAX_REQUESTS,
			resetTime: new Date(now.getTime() + RATE_LIMIT_CONFIG.WINDOW_MS),
			resetInSeconds: RATE_LIMIT_CONFIG.WINDOW_MS / 1000,
		};
	}
}

/**
 * Record rate limit usage for /cf endpoint
 *
 * Records a request in the rate limiting system for tracking purposes.
 * Uses the webhooks table with a special source identifier for rate limit tracking.
 * This should be called after successful rate limit check and request processing.
 *
 * @param requestId - Request ID for tracking and correlation
 * @returns Promise resolving when the rate limit record is stored
 *
 * @example
 * ```typescript
 * // After successful /cf request processing
 * await recordCfRateLimitUsage("req-123");
 * ```
 *
 * @since 1.0.0
 */
export async function recordCfRateLimitUsage(): Promise<void> {
	const db = getDb();
	const requestId = getRequestId();

	try {
		await db.insert(dbSchema.webhooks).values({
			requestId: `cf-rate-limit-${requestId}`,
			payload: {
				type: "rate_limit_tracking",
				endpoint: "/cf",
				timestamp: new Date().toISOString(),
			},
			source: RATE_LIMIT_CONFIG.SOURCE,
		});

		logDebug("Recorded /cf rate limit usage", {
			source: RATE_LIMIT_CONFIG.SOURCE,
		});
	} catch (error) {
		// Log error but don't fail the request
		logWarn("Failed to record rate limit usage", {
			error: String(error),
		});
	}
}

/**
 * Clean up old rate limit records
 *
 * Removes rate limit tracking records older than the rate limit window
 * to prevent database bloat. Should be called periodically or during
 * rate limit checks to maintain database performance.
 *
 * @returns Promise resolving to the number of cleaned up records
 *
 * @example
 * ```typescript
 * const cleanedCount = await cleanupOldRateLimitRecords("req-123");
 * console.log(`Cleaned up ${cleanedCount} old rate limit records`);
 * ```
 *
 * @since 1.0.0
 */
export async function cleanupOldRateLimitRecords(): Promise<number> {
	const db = getDb();
	const cutoffTime = new Date(Date.now() - RATE_LIMIT_CONFIG.WINDOW_MS * 2); // Keep 2x window for safety

	try {
		const deletedRecords = await db
			.delete(dbSchema.webhooks)
			.where(
				and(
					// Filter by request ID pattern for rate limit tracking
					like(dbSchema.webhooks.requestId, "cf-rate-limit-%"),
					// Use lt instead of gte for deletion
					lt(dbSchema.webhooks.createdAt, cutoffTime),
				),
			)
			.returning({ id: dbSchema.webhooks.id });

		const cleanedCount = deletedRecords.length;

		if (cleanedCount > 0) {
			logInfo("Cleaned up old rate limit records", {
				cleanedCount,
				cutoffTime: cutoffTime.toISOString(),
			});
		}

		return cleanedCount;
	} catch (error) {
		logWarn("Failed to cleanup old rate limit records", {
			error: String(error),
		});
		return 0;
	}
}

/**
 * Get rate limit status without checking
 *
 * Retrieves current rate limit status information without performing
 * a rate limit check. Useful for providing rate limit information
 * in API responses or monitoring.
 *
 * @returns Promise resolving to current rate limit status
 *
 * @example
 * ```typescript
 * const status = await getRateLimitStatus("req-123");
 * console.log(`Current usage: ${status.requestCount}/${status.maxRequests}`);
 * ```
 *
 * @since 1.0.0
 */
export async function getRateLimitStatus(): Promise<RateLimitResult> {
	// Use the same logic as checkCfRateLimit but without the side effects
	return await checkCfRateLimit();
}
