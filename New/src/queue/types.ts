/**
 * Webhook Queue System Types
 *
 * Type definitions for the webhook queue system including queue management,
 * duplicate prevention, timeout handling, and processing results.
 *
 * @fileoverview Type definitions for webhook queue system
 * @version 1.0.0
 * @since 2025-08-06
 */

import type { APWebhookPayload } from "@type";
import type { CCWebhookPayload } from "@/processors/ccWebhook";

/**
 * Webhook queue status types
 */
export type WebhookQueueStatus =
	| "pending"
	| "processing"
	| "completed"
	| "failed"
	| "timeout"
	| "duplicate_skipped";

/**
 * Webhook queue log status types (final states only)
 */
export type WebhookQueueLogStatus =
	| "completed"
	| "failed"
	| "timeout"
	| "duplicate_skipped";

/**
 * Alert types for monitoring
 */
export type AlertType =
	| "webhook_timeout"
	| "high_failure_rate"
	| "queue_backlog"
	| "duplicate_storm"
	| "processing_error";

/**
 * Alert severity levels
 */
export type AlertSeverity = "low" | "medium" | "high" | "critical";

/**
 * Lock types for duplicate prevention
 */
export type LockType =
	| "patient_sync"
	| "appointment_sync"
	| "custom_field_sync";

/**
 * Platform source types
 */
export type PlatformSource = "cc" | "ap";

/**
 * Discriminated union types for webhook payloads
 *
 * These types use the 'source' field as a discriminator to ensure
 * type safety when processing webhooks from different platforms.
 */

/**
 * CliniCore webhook queue item for insertion
 */
export interface CCWebhookQueueInsert {
	source: "cc";
	entityType: string;
	entityId: string;
	patientId?: string;
	appointmentId?: string;
	payload: CCWebhookPayload;
	priority?: number;
	maxRetries?: number;
}

/**
 * AutoPatient webhook queue item for insertion
 */
export interface APWebhookQueueInsert {
	source: "ap";
	entityType: string;
	entityId: string;
	patientId?: string;
	appointmentId?: string;
	payload: APWebhookPayload;
	priority?: number;
	maxRetries?: number;
}

/**
 * Webhook queue item for insertion (discriminated union)
 */
export type WebhookQueueInsert = CCWebhookQueueInsert | APWebhookQueueInsert;

/**
 * CliniCore webhook queue item from database
 */
export interface CCWebhookQueueItem {
	id: string;
	createdAt: Date;
	updatedAt: Date;
	status: WebhookQueueStatus;
	priority: number;
	source: "cc";
	entityType: string;
	entityId: string;
	patientId?: string;
	appointmentId?: string;
	payload: CCWebhookPayload;
	startedAt?: Date;
	completedAt?: Date;
	retryCount: number;
	maxRetries: number;
	nextRetryAt?: Date;
	errorMessage?: string;
	processingTimeMs?: number;
	duplicateOf?: string;
	duplicateReason?: string;
}

/**
 * AutoPatient webhook queue item from database
 */
export interface APWebhookQueueItem {
	id: string;
	createdAt: Date;
	updatedAt: Date;
	status: WebhookQueueStatus;
	priority: number;
	source: "ap";
	entityType: string;
	entityId: string;
	patientId?: string;
	appointmentId?: string;
	payload: APWebhookPayload;
	startedAt?: Date;
	completedAt?: Date;
	retryCount: number;
	maxRetries: number;
	nextRetryAt?: Date;
	errorMessage?: string;
	processingTimeMs?: number;
	duplicateOf?: string;
	duplicateReason?: string;
}

/**
 * Webhook queue item from database (discriminated union)
 */
export type WebhookQueueItem = CCWebhookQueueItem | APWebhookQueueItem;

/**
 * CliniCore webhook queue log item
 */
export interface CCWebhookQueueLogItem {
	id: string;
	createdAt: Date;
	updatedAt: Date;
	status: WebhookQueueLogStatus;
	priority: number;
	source: "cc";
	entityType: string;
	entityId: string;
	patientId?: string;
	appointmentId?: string;
	payload: CCWebhookPayload;
	startedAt?: Date;
	completedAt?: Date;
	retryCount: number;
	errorMessage?: string;
	processingTimeMs?: number;
	duplicateOf?: string;
	duplicateReason?: string;
}

/**
 * AutoPatient webhook queue log item
 */
export interface APWebhookQueueLogItem {
	id: string;
	createdAt: Date;
	updatedAt: Date;
	status: WebhookQueueLogStatus;
	priority: number;
	source: "ap";
	entityType: string;
	entityId: string;
	patientId?: string;
	appointmentId?: string;
	payload: APWebhookPayload;
	startedAt?: Date;
	completedAt?: Date;
	retryCount: number;
	errorMessage?: string;
	processingTimeMs?: number;
	duplicateOf?: string;
	duplicateReason?: string;
}

/**
 * Webhook queue log item (discriminated union)
 */
export type WebhookQueueLogItem = CCWebhookQueueLogItem | APWebhookQueueLogItem;

/**
 * Duplicate prevention lock
 */
export interface DuplicatePreventionLock {
	id: string;
	createdAt: Date;
	updatedAt: Date;
	lockType: LockType;
	patientId?: string;
	appointmentId?: string;
	source: PlatformSource;
	webhookId: string;
	expiresAt: Date;
	context?: Record<string, unknown>;
}

/**
 * Alert item
 */
export interface Alert {
	id: string;
	createdAt: Date;
	updatedAt: Date;
	alertType: AlertType;
	severity: AlertSeverity;
	title: string;
	description: string;
	webhookId?: string;
	patientId?: string;
	appointmentId?: string;
	context?: Record<string, unknown>;
	resolved: number; // 0 = false, 1 = true
	resolvedAt?: Date;
	resolvedBy?: string;
}

/**
 * API-triggered duplicate detection result
 */
export interface ApiTriggeredDuplicateResult {
	/** Whether this webhook is an API-triggered duplicate */
	isDuplicate: boolean;
	/** ID of the original webhook that triggered the API call */
	originalWebhookId?: string;
	/** Reason for duplicate detection */
	reason?: string;
	/** Lock reason that caused the duplicate detection */
	lockReason?: string;
	/** Confidence level of duplicate detection */
	confidence?: "high" | "medium" | "low";
}

/**
 * Enhanced duplicate detection result
 */
export interface DuplicateDetectionResult {
	isDuplicate: boolean;
	originalWebhookId?: string;
	reason?: string;
	lockType?: LockType;
	/** API-triggered duplicate details (highest priority) */
	apiTriggered?: ApiTriggeredDuplicateResult;
	/** Confidence level of duplicate detection */
	confidence?: "high" | "medium" | "low";
}

/**
 * Webhook processing result
 */
export interface WebhookProcessingResult {
	webhookId: string;
	status: WebhookQueueStatus;
	processingTimeMs?: number;
	errorMessage?: string;
	duplicateOf?: string;
	duplicateReason?: string;
}

/**
 * Batch processing result
 */
export interface BatchProcessingResult {
	success: boolean;
	processedCount: number;
	batchResults: WebhookProcessingResult[];
	retryCount: number;
	alertsCreated: number;
}

/**
 * Queue processing options
 */
export interface QueueProcessingOptions {
	batchSize?: number;
	maxProcessingTimeMs?: number;
	skipDuplicateCheck?: boolean;
}

/**
 * Timeout detection result
 */
export interface TimeoutDetectionResult {
	timedOutWebhooks: WebhookQueueItem[];
	alertsCreated: number;
}

/**
 * Lock creation options for duplicate prevention
 */
export interface LockCreationOptions {
	/** Webhook ID that is making the API call */
	webhookId: string;
	/** Target platform that will receive the API call */
	targetPlatform: PlatformSource;
	/** Patient ID for patient-related locks */
	patientId?: string;
	/** Appointment ID for appointment-related locks */
	appointmentId?: string;
	/** Lock duration in seconds (default: 60) */
	lockDurationSeconds?: number;
}

/**
 * Webhook data for duplicate checking
 */
export interface WebhookForDuplicateCheck {
	source: PlatformSource;
	entityType: string;
	patientId?: string;
	appointmentId?: string;
}

/**
 * Queue statistics
 */
export interface QueueStatistics {
	pending: number;
	processing: number;
	completed: number;
	failed: number;
	timeout: number;
	duplicateSkipped: number;
	totalInQueue: number;
	oldestPendingAge?: number; // in milliseconds
}

/**
 * Type guard functions for webhook payload discrimination
 *
 * These functions provide type-safe ways to determine the specific
 * webhook type based on the source field.
 */

/**
 * Type guard to check if a webhook queue item is from CliniCore
 */
export function isCCWebhookQueueItem(
	webhook: WebhookQueueItem,
): webhook is CCWebhookQueueItem {
	return webhook.source === "cc";
}

/**
 * Type guard to check if a webhook queue item is from AutoPatient
 */
export function isAPWebhookQueueItem(
	webhook: WebhookQueueItem,
): webhook is APWebhookQueueItem {
	return webhook.source === "ap";
}

/**
 * Type guard to check if a webhook queue insert is from CliniCore
 */
export function isCCWebhookQueueInsert(
	webhook: WebhookQueueInsert,
): webhook is CCWebhookQueueInsert {
	return webhook.source === "cc";
}

/**
 * Type guard to check if a webhook queue insert is from AutoPatient
 */
export function isAPWebhookQueueInsert(
	webhook: WebhookQueueInsert,
): webhook is APWebhookQueueInsert {
	return webhook.source === "ap";
}

/**
 * Type guard to check if a webhook queue log item is from CliniCore
 */
export function isCCWebhookQueueLogItem(
	webhook: WebhookQueueLogItem,
): webhook is CCWebhookQueueLogItem {
	return webhook.source === "cc";
}

/**
 * Type guard to check if a webhook queue log item is from AutoPatient
 */
export function isAPWebhookQueueLogItem(
	webhook: WebhookQueueLogItem,
): webhook is APWebhookQueueLogItem {
	return webhook.source === "ap";
}
