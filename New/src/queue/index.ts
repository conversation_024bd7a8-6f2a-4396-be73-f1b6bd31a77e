/**
 * Webhook Queue System
 *
 * Main entry point for the webhook queue system providing a unified interface
 * for queue management, processing, and monitoring. Integrates with the existing
 * fire-and-forget utilities for immediate processing triggers.
 *
 * @fileoverview Webhook queue system main module
 * @version 1.0.0
 * @since 2025-08-06
 */

// API handlers
export {
	addWebhookToQueue,
	getQueueStatistics,
	processQueue,
} from "@/handlers/queueHandler";
// Utility functions
export {
	fireAndForgetQueueProcessing,
	fireAndForgetWebhookIngestion,
} from "./fireAndForgetIntegration";
export { WebhookTimeoutDetector } from "./timeoutDetector";
// Type definitions
export type {
	Alert,
	AlertSeverity,
	AlertType,
	BatchProcessingResult,
	DuplicateDetectionResult,
	DuplicatePreventionLock,
	LockType,
	PlatformSource,
	QueueProcessingOptions,
	QueueStatistics,
	TimeoutDetectionResult,
	WebhookProcessingResult,
	WebhookQueueInsert,
	WebhookQueueItem,
	WebhookQueueLogItem,
	WebhookQueueLogStatus,
	WebhookQueueStatus,
} from "./types";
// Core queue management
export { WebhookQueueManager } from "./WebhookQueueManager";
