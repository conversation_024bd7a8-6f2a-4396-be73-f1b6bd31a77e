/**
 * AutoPatient (AP) API Client
 *
 * Comprehensive API client for AutoPatient system providing all CRUD operations
 * for contacts, appointments, custom fields, and notes.
 *
 * Features:
 * - Complete feature parity with v3Integration
 * - TypeScript typing and error handling
 * - Configuration from configs.ts
 * - Data cleaning utilities
 */

import getConfig from "@config";
import type {
	APGetCustomFieldType,
	APPostCustomfieldType,
	GetAPAppointmentType,
	GetAPContactType,
	GetAPNoteType,
	PostAPAppointmentType,
	PostAPContactType,
	PutAPAppointmentType,
} from "@type/APTypes";
import { apiResponseCache } from "@utils/advancedCache";
import cleanData from "@utils/cleanData";
import { logDebug, logError, logWarn } from "@/utils/logger";

/**
 * Backend search endpoint response type
 * This represents the raw response structure from the backend search endpoint
 */
interface BackendSearchCustomFieldType {
	_id: string;
	id: string;
	name: string;
	dataType: string;
	placeholder?: string;
	fieldKey?: string;
	position?: number;
	allowCustomOption?: boolean;
	dateAdded?: string;
	documentType?: string;
	locationId?: string;
	model?: string;
	parentId?: string;
	standard?: boolean;
	fieldsCount?: number;
	// Additional properties that might be present
	acceptedFormat?: string[];
	isMultipleFile?: boolean;
	maxNumberOfFiles?: number;
	textBoxListOptions?: {
		label: string;
		prefillValue: string;
	}[];
	picklistOptions?: string[];
	isAllowedCustomOption?: boolean;
}

/**
 * Convert backend search response field to standard APGetCustomFieldType
 * Ensures compatibility with existing codebase that expects APGetCustomFieldType format
 */
function convertBackendFieldToAPType(
	backendField: BackendSearchCustomFieldType,
): APGetCustomFieldType {
	return {
		// Core required fields
		id: backendField.id || backendField._id, // Prefer 'id', fallback to '_id'
		name: backendField.name,
		dataType: backendField.dataType,

		// Optional fields with proper mapping
		placeholder: backendField.placeholder,
		acceptedFormat: backendField.acceptedFormat,
		isMultipleFile: backendField.isMultipleFile,
		maxNumberOfFiles: backendField.maxNumberOfFiles,
		textBoxListOptions: backendField.textBoxListOptions,
		position: backendField.position,
		fieldKey: backendField.fieldKey,
		picklistOptions: backendField.picklistOptions,
		isAllowedCustomOption:
			backendField.isAllowedCustomOption || backendField.allowCustomOption,
	};
}

/**
 * Backend search endpoint response type
 * This represents the raw response structure from the backend search endpoint
 */
interface BackendSearchCustomFieldType {
	_id: string;
	id: string;
	name: string;
	dataType: string;
	placeholder?: string;
	fieldKey?: string;
	position?: number;
	allowCustomOption?: boolean;
	dateAdded?: string;
	documentType?: string;
	locationId?: string;
	model?: string;
	parentId?: string;
	standard?: boolean;
	fieldsCount?: number;
	// Additional properties that might be present
	acceptedFormat?: string[];
	isMultipleFile?: boolean;
	maxNumberOfFiles?: number;
	textBoxListOptions?: {
		label: string;
		prefillValue: string;
	}[];
	picklistOptions?: string[];
	isAllowedCustomOption?: boolean;
}

/**
 * HTTP request options interface
 */
interface RequestOptions {
	url: string;
	method: "GET" | "POST" | "PUT" | "DELETE";
	data?: unknown;
	params?: Record<string, string | number | boolean>;
	invalidateCache?: boolean;
}

/**
 * Standard API response interface for delete operations
 */
interface DeleteResponse extends Record<string, unknown> {
	succeded: boolean;
}

/**
 * Standard API response interface for appointment operations
 */
interface AppointmentUpdateResponse extends Record<string, unknown> {
	appointment: GetAPAppointmentType;
}

/**
 * Base HTTP client for AutoPatient API v2 with intelligent caching
 */
const apRequestV2 = async <
	T extends Record<string, unknown> = Record<string, unknown>,
>(
	options: RequestOptions,
): Promise<T> => {
	const { url, method, data, params, invalidateCache = false } = options;

	// Build full URL with base domain
	const baseUrl = getConfig("apApiDomain");
	let fullUrl = `${baseUrl}${url}`;

	// Add query parameters if provided
	if (params) {
		const searchParams = new URLSearchParams();
		Object.entries(params).forEach(([key, value]) => {
			searchParams.append(key, String(value));
		});
		fullUrl += `?${searchParams.toString()}`;
	}

	// Generate cache key for GET requests
	const cacheKey = apiResponseCache.generateKey(method, url, params);

	// Check cache for GET requests (unless cache invalidation is requested)
	if (method === "GET" && !invalidateCache) {
		const cachedData = apiResponseCache.get(cacheKey);
		if (cachedData) {
			return cachedData as T;
		}
	}

	// Prepare request headers
	const headers: Record<string, string> = {
		Authorization: `Bearer ${getConfig("apApiKey")}`,
		"Content-Type": "application/json",
		"Accept-Encoding": "application/json",
		Accept: "application/json",
		Version: "2021-04-15",
	};

	try {
		const response = await fetch(fullUrl, {
			method,
			headers,
			body: data ? JSON.stringify(data) : undefined,
		});

		if (!response.ok) {
			const errorText = await response.text();

			// Enhanced API error logging for debugging
			console.log("=".repeat(80));
			console.log("AP API ERROR DETAILS:");
			console.log("=".repeat(80));
			console.log("Timestamp:", new Date().toISOString());
			console.log("Request Details:");
			console.log("  Method:", method);
			console.log("  URL:", fullUrl);
			console.log("  Headers:", {
				...headers,
				Authorization: headers.Authorization ? "[REDACTED]" : undefined
			});
			if (data) {
				console.log("  Request Body:", JSON.stringify(data, null, 2));
			}
			console.log("Response Details:");
			console.log("  Status:", response.status, response.statusText);
			console.log("  Response Headers:", Object.fromEntries(response.headers.entries()));
			console.log("  Response Body:", errorText);
			console.log("=".repeat(80));

			let errorMessage: string;

			try {
				const errorData = JSON.parse(errorText);

				// Handle nested error objects properly
				let extractedErrorMessage: string | undefined;
				if (errorData.error) {
					if (typeof errorData.error === "string") {
						extractedErrorMessage = errorData.error;
					} else if (typeof errorData.error === "object" && errorData.error !== null) {
						// Handle nested error objects like {"error": {"message": "..."}}
						const errorObj = errorData.error as Record<string, unknown>;
						extractedErrorMessage =
							(typeof errorObj.message === "string" ? errorObj.message : undefined) ||
							(typeof errorObj.detail === "string" ? errorObj.detail : undefined) ||
							(typeof errorObj.title === "string" ? errorObj.title : undefined) ||
							JSON.stringify(errorData.error);
					}
				}

				errorMessage =
					errorData.message ||
					extractedErrorMessage ||
					`HTTP ${response.status}: ${response.statusText}`;
			} catch {
				errorMessage = `HTTP ${response.status}: ${response.statusText}`;
			}

			throw new Error(`AP API Error: ${errorMessage}`);
		}

		const responseData = await response.json();

		// Cache successful GET responses
		if (method === "GET") {
			apiResponseCache.set(cacheKey, responseData);
		}

		// Invalidate related cache entries for mutating operations
		if (method === "POST" || method === "PUT" || method === "DELETE") {
			const invalidatedCount = apiResponseCache.invalidatePattern(url);
			if (invalidatedCount > 0) {
				logDebug(
					`AP API: Invalidated ${invalidatedCount} cache entries for ${method} ${url}`,
				);
			}
		}

		return responseData as T;
	} catch (error) {
		if (error instanceof Error) {
			// Enhanced logging for network/connection errors
			console.log("=".repeat(80));
			console.log("AP API NETWORK ERROR DETAILS:");
			console.log("=".repeat(80));
			console.log("Timestamp:", new Date().toISOString());
			console.log("Error Type:", error.name);
			console.log("Error Message:", error.message);
			console.log("Request Details:");
			console.log("  Method:", method);
			console.log("  URL:", fullUrl);
			console.log("  Headers:", {
				...headers,
				Authorization: headers.Authorization ? "[REDACTED]" : undefined
			});
			if (data) {
				console.log("  Request Body:", JSON.stringify(data, null, 2));
			}
			if (error.stack) {
				console.log("Stack Trace:", error.stack.split('\n').slice(0, 5).join('\n'));
			}
			console.log("=".repeat(80));

			throw new Error(`AP API Request Failed: ${error.message}`);
		}

		// Enhanced logging for unknown errors
		console.log("=".repeat(80));
		console.log("AP API UNKNOWN ERROR DETAILS:");
		console.log("=".repeat(80));
		console.log("Timestamp:", new Date().toISOString());
		console.log("Error:", error);
		console.log("Request Details:");
		console.log("  Method:", method);
		console.log("  URL:", fullUrl);
		console.log("=".repeat(80));

		throw new Error("AP API Request Failed: Unknown error");
	}
};

/**
 * Contact operations
 */
export const contactReq = {
	/**
	 * Get a contact by ID
	 */
	get: async (
		id: string,
		invalidateCache?: boolean,
	): Promise<GetAPContactType> => {
		const response = await apRequestV2<{ contact: GetAPContactType }>({
			url: `/contacts/${id}`,
			method: "GET",
			invalidateCache,
		});
		return response.contact;
	},

	/**
	 * Create a new contact
	 */
	create: async (data: PostAPContactType): Promise<GetAPContactType> => {
		const locationId = getConfig("locationID");
		const response = await apRequestV2<{ contact: GetAPContactType }>({
			url: "/contacts/",
			method: "POST",
			data: { ...cleanData(data), locationId },
		});
		return response.contact;
	},

	/**
	 * Upsert a contact (create or update)
	 */
	upsert: async (data: PostAPContactType): Promise<GetAPContactType> => {
		const locationId = getConfig("locationID");
		const response = await apRequestV2<{ contact: GetAPContactType }>({
			url: "/contacts/upsert/",
			method: "POST",
			data: { ...cleanData(data), locationId },
		});
		return response.contact;
	},

	/**
	 * Update an existing contact
	 */
	update: async (
		id: string,
		data: PostAPContactType,
	): Promise<GetAPContactType> => {
		// Use custom cleanData options to preserve field_value properties for TEXTBOX_LIST fields
		const cleanedData = cleanData(data, {
			keepValue: (_, key) => {
				// Always preserve field_value properties, even if they're empty objects
				if (key === "field_value") {
					return true;
				}
				return false;
			},
		});

		const response = await apRequestV2<{ contact: GetAPContactType }>({
			url: `/contacts/${id}`,
			method: "PUT",
			data: cleanedData,
		});
		return response.contact;
	},

	/**
	 * Delete a contact
	 */
	delete: async (id: string): Promise<DeleteResponse> => {
		return apRequestV2<DeleteResponse>({
			url: `/contacts/${id}/`,
			method: "DELETE",
		});
	},

	/**
	 * Get appointments for a contact
	 */
	appointments: async (
		contactId: string,
		invalidateCache?: boolean,
	): Promise<{ appointments: GetAPAppointmentType[] }> => {
		return apRequestV2<{ appointments: GetAPAppointmentType[] }>({
			url: `/contacts/${contactId}/appointments/`,
			method: "GET",
			invalidateCache,
		});
	},

	/**
	 * Get all contacts with optional filtering
	 */
	all: async (params?: {
		limit?: number;
		query?: string;
		startAfter?: number;
		startAfterId?: string;
		invalidateCache?: boolean;
	}): Promise<GetAPContactType[]> => {
		const { invalidateCache, ...queryParams } = params || {};
		const locationId = getConfig("locationID") as string;
		const response = await apRequestV2<{ contacts: GetAPContactType[] }>({
			url: "/contacts/",
			method: "GET",
			params: { ...queryParams, locationId },
			invalidateCache,
		});
		return response.contacts;
	},
};

/**
 * Custom field operations
 */
export const apCustomfield = {
	/**
	 * Get a custom field by ID
	 */
	get: async (
		id: string,
		invalidateCache?: boolean,
	): Promise<APGetCustomFieldType> => {
		const locationId = getConfig("locationID");
		const response = await apRequestV2<{ customField: APGetCustomFieldType }>({
			url: `/locations/${locationId}/customFields/${id}/`,
			method: "GET",
			invalidateCache,
		});
		return response.customField;
	},

	/**
	 * Get all custom fields
	 */
	all: async (invalidateCache?: boolean): Promise<APGetCustomFieldType[]> => {
		const locationId = getConfig("locationID");
		const response = await apRequestV2<{
			customFields: APGetCustomFieldType[];
		}>({
			url: `/locations/${locationId}/customFields/?model=all`,
			method: "GET",
			invalidateCache,
		});
		return response.customFields;
	},

	/**
	 * Get all custom fields with optional parent filtering
	 *
	 * This method implements conditional logic based on the apCustomFieldsParents configuration:
	 * - When apCustomFieldsParents array is empty: fetches ALL custom fields (same as all() method)
	 * - When apCustomFieldsParents array contains parent IDs: fetches only custom fields under those specific parent IDs
	 *
	 * Uses the /customFields/search endpoint with query parameters for parent filtering:
	 * - parentId: filters by specific parent ID
	 * - documentType: set to "field"
	 * - model: set to "all"
	 * - includeStandards: set to true
	 *
	 * @param invalidateCache - Whether to invalidate the cache for this request
	 * @returns Promise resolving to array of custom fields matching the filter criteria
	 *
	 * @example
	 * ```typescript
	 * // Get all fields (when apCustomFieldsParents is empty)
	 * const allFields = await apCustomfield.allWithParentFilter();
	 *
	 * // Get filtered fields (when apCustomFieldsParents has parent IDs)
	 * // Configuration: apCustomFieldsParents: ["parent1", "parent2"]
	 * const filteredFields = await apCustomfield.allWithParentFilter();
	 * ```
	 */
	allWithParentFilter: async (
		invalidateCache?: boolean,
	): Promise<APGetCustomFieldType[]> => {
		const locationId = getConfig("locationID");
		const apCustomFieldsParents = getConfig(
			"apCustomFieldsParents",
		) as string[];

		// If no parent filters are configured, fetch all custom fields
		if (apCustomFieldsParents.length === 0) {
			logDebug("No parent filters configured, fetching all custom fields", {
				locationId,
			});
			return apCustomfield.all(invalidateCache);
		}

		const allFilteredFields: APGetCustomFieldType[] = [];

		// Make API calls for each parent ID using the backend URL
		for (const parentId of apCustomFieldsParents) {
			try {
				logDebug("Fetching custom fields for parent", {
					locationId,
					parentId,
				});

				// Use the backend URL directly for the search endpoint
				const backendUrl = "https://backend.leadconnectorhq.com";
				const searchUrl = `${backendUrl}/locations/${locationId}/customFields/search`;

				// Build query parameters
				const searchParams = new URLSearchParams({
					parentId,
					documentType: "field",
					model: "all",
					includeStandards: "true",
					skip: "0",
					limit: "1000", // Set a reasonable limit to get all fields
				});

				const fullSearchUrl = `${searchUrl}?${searchParams.toString()}`;

				// Make direct fetch request to backend URL with proper headers
				const apApiKey = getConfig("apApiKey");
				const fetchOptions: RequestInit = {
					method: "GET",
					headers: {
						Authorization: `Bearer ${apApiKey}`,
						Version: "2021-04-15",
						"Content-Type": "application/json",
					},
				};

				// Add cache control if invalidateCache is true
				if (invalidateCache) {
					fetchOptions.cache = "no-store";
				}

				const response = await fetch(fullSearchUrl, fetchOptions);

				if (!response.ok) {
					throw new Error(`HTTP ${response.status}: ${response.statusText}`);
				}

				const responseData = (await response.json()) as {
					customFields: BackendSearchCustomFieldType[];
					totalItems?: number;
					traceId?: string;
				};

				if (responseData.customFields && responseData.customFields.length > 0) {
					// Convert backend response fields to standard APGetCustomFieldType format
					const convertedFields = responseData.customFields.map(
						convertBackendFieldToAPType,
					);
					allFilteredFields.push(...convertedFields);

					logDebug("Successfully fetched custom fields for parent", {
						locationId,
						parentId,
						fieldsCount: responseData.customFields.length,
						totalItems: responseData.totalItems,
						traceId: responseData.traceId,
					});
				} else {
					logDebug("No custom fields found for parent", {
						locationId,
						parentId,
					});
				}
			} catch (error) {
				logError("Failed to fetch custom fields for parent", {
					locationId,
					parentId,
					error: error instanceof Error ? error.message : String(error),
				});
				// Continue with other parent IDs even if one fails
			}
		}

		logDebug("Completed parent-filtered custom fields fetch", {
			locationId,
			totalParents: apCustomFieldsParents.length,
			totalFieldsFound: allFilteredFields.length,
		});

		return allFilteredFields;
	},

	/**
	 * Create a new custom field using the location-specific endpoint
	 * Note: The v2 /custom-fields/ endpoint only supports Custom Objects and Company fields,
	 * not Contact fields yet. We must use the location-specific endpoint for contacts.
	 *
	 * Enhanced with production-ready validation and error handling.
	 */
	create: async (
		data: APPostCustomfieldType,
	): Promise<APGetCustomFieldType> => {
		// Input validation
		if (!data) {
			throw new Error("AP Custom Field creation data is required");
		}

		if (!data.name || data.name.trim().length === 0) {
			throw new Error("AP Custom Field name is required and cannot be empty");
		}

		if (!data.dataType || data.dataType.trim().length === 0) {
			throw new Error(
				"AP Custom Field dataType is required and cannot be empty",
			);
		}

		// Validate field name doesn't contain invalid characters
		const invalidChars = /[<>:"/\\|?*]/;
		if (invalidChars.test(data.name)) {
			throw new Error(
				`AP Custom Field name "${data.name}" contains invalid characters`,
			);
		}

		const locationId = getConfig("locationID");
		if (!locationId) {
			throw new Error("Location ID is required for AP custom field creation");
		}

		// Debug logging before cleaning
		logDebug("AP Custom Field creation - before cleaning", {
			fieldName: data.name,
			dataType: data.dataType,
			fieldKey: data.fieldKey,
			documentType: data.documentType,
			showInForms: data.showInForms,
			model: data.model,
			description: data.description,
			hasTextBoxListOptions: Boolean(data.textBoxListOptions),
			textBoxListOptionsLength: data.textBoxListOptions?.length || 0,
			textBoxListOptions: data.textBoxListOptions,
		});

		// Clean and prepare data with detailed logging
		logDebug("AP Custom Field creation - before cleanData transformation", {
			originalData: data,
			originalKeys: Object.keys(data),
			// Check for properties that might be removed by cleanData
			hasEmptyStrings: Object.entries(data).some(([, value]) => value === ""),
			hasEmptyArrays: Object.entries(data).some(
				([, value]) => Array.isArray(value) && value.length === 0,
			),
			hasEmptyObjects: Object.entries(data).some(
				([, value]) =>
					typeof value === "object" &&
					value !== null &&
					!Array.isArray(value) &&
					Object.keys(value).length === 0,
			),
			// Specific checks for TEXTBOX_LIST fields
			isTextboxList: data.dataType === "TEXTBOX_LIST",
			textBoxListOptionsBeforeCleaning: data.textBoxListOptions,
		});

		// Use custom cleanData options to preserve textBoxListOptions structure
		const cleanedData = cleanData(data, {
			// Preserve empty strings in textBoxListOptions to maintain required structure
			keepValue: (_, key) => {
				// Always keep prefillValue properties, even if they're empty strings
				if (key === "prefillValue") {
					return true;
				}
				// Always keep textBoxListOptions array, even if it contains empty values
				if (key === "textBoxListOptions") {
					return true;
				}
				return false;
			},
		});

		logDebug("AP Custom Field creation - after cleanData transformation", {
			cleanedData: cleanedData,
			cleanedKeys: Object.keys(cleanedData),
			removedKeys: Object.keys(data).filter((key) => !(key in cleanedData)),
			// Check what happened to specific properties
			textBoxListOptionsAfterCleaning: cleanedData.textBoxListOptions,
			wasTextBoxListOptionsRemoved:
				"textBoxListOptions" in data && !("textBoxListOptions" in cleanedData),
		});

		// Debug logging after cleaning
		logDebug("AP Custom Field creation - after cleaning", {
			fieldName: cleanedData.name,
			dataType: cleanedData.dataType,
			fieldKey: cleanedData.fieldKey,
			documentType: cleanedData.documentType,
			showInForms: cleanedData.showInForms,
			model: cleanedData.model,
			description: cleanedData.description,
			hasTextBoxListOptions: Boolean(cleanedData.textBoxListOptions),
			textBoxListOptionsLength: cleanedData.textBoxListOptions?.length || 0,
			textBoxListOptions: cleanedData.textBoxListOptions,
		});

		try {
			// Debug logging - final request payload being sent to AP API
			logDebug("AP Custom Field creation - final request payload", {
				endpoint: `/locations/${locationId}/customFields/`,
				method: "POST",
				finalPayload: cleanedData,
				payloadKeys: Object.keys(cleanedData),
				payloadSize: JSON.stringify(cleanedData).length,
				// Detailed breakdown of key properties
				name: cleanedData.name,
				dataType: cleanedData.dataType,
				fieldKey: cleanedData.fieldKey,
				documentType: cleanedData.documentType,
				showInForms: cleanedData.showInForms,
				model: cleanedData.model,
				description: cleanedData.description,
				textBoxListOptions: cleanedData.textBoxListOptions,
				acceptedFormat: cleanedData.acceptedFormat,
				placeholder: cleanedData.placeholder,
				position: cleanedData.position,
				parentId: cleanedData.parentId,
				// Check for any unexpected properties
				hasAcceptedFormat: "acceptedFormat" in cleanedData,
				hasUnexpectedProps: Object.keys(cleanedData).filter(
					(key) =>
						![
							"name",
							"dataType",
							"fieldKey",
							"documentType",
							"showInForms",
							"model",
							"description",
							"textBoxListOptions",
							"placeholder",
							"position",
							"parentId",
							"isMultipleFile",
							"maxNumberOfFiles",
							"options",
							"locationId",
							"objectKey",
							"allowCustomOption",
							"maxFileLimit",
							"acceptedFormat",
						].includes(key),
				),
			});

			const response = await apRequestV2<{ customField: APGetCustomFieldType }>(
				{
					url: `/locations/${locationId}/customFields/`,
					method: "POST",
					data: cleanedData,
				},
			);

			if (!response.customField) {
				throw new Error(
					"AP API returned invalid response: missing customField",
				);
			}

			logDebug("AP Custom Field creation - successful response", {
				fieldName: data.name,
				createdFieldId: response.customField?.id,
				createdFieldName: response.customField?.name,
				createdFieldType: response.customField?.dataType,
			});

			return response.customField;
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : String(error);

			// Check if this is a conflict error that can potentially be resolved
			const isConflictError =
				errorMessage.toLowerCase().includes("already exists") ||
				errorMessage.toLowerCase().includes("duplicate") ||
				errorMessage.toLowerCase().includes("conflict") ||
				errorMessage.toLowerCase().includes("name is already taken");

			// Enhanced error handling with appropriate log level
			const logData = {
				fieldName: data.name,
				dataType: data.dataType,
				endpoint: `/locations/${locationId}/customFields/`,
				error: errorMessage,
				// Log the exact payload that failed
				failedPayload: cleanedData,
				payloadKeys: Object.keys(cleanedData),
				// Check for specific error patterns
				isAcceptedFormatError: errorMessage
					.toLowerCase()
					.includes("acceptedformat"),
				isValidationError: errorMessage.toLowerCase().includes("invalid"),
				isTextBoxListError: errorMessage.toLowerCase().includes("textboxlist"),
				isConflictError,
			};

			if (isConflictError) {
				// Log conflicts at WARN level since they may be automatically resolved
				logWarn(
					"AP Custom Field creation - conflict detected (may be auto-resolved)",
					logData,
				);
			} else {
				// Log other errors at ERROR level as they are genuine failures
				logError("AP Custom Field creation - API error", logData);
			}

			if (error instanceof Error) {
				throw new Error(
					`Failed to create AP custom field "${data.name}": ${error.message}`,
				);
			}
			throw new Error(
				`Failed to create AP custom field "${data.name}": Unknown error`,
			);
		}
	},

	/**
	 * Update an existing custom field
	 */
	update: async (
		id: string,
		data: APPostCustomfieldType,
	): Promise<APGetCustomFieldType> => {
		const locationId = getConfig("locationID");
		const response = await apRequestV2<{ customField: APGetCustomFieldType }>({
			url: `/locations/${locationId}/customFields/${id}/`,
			method: "PUT",
			data,
		});
		return response.customField;
	},

	/**
	 * Delete a custom field
	 */
	delete: async (id: string): Promise<boolean> => {
		const locationId = getConfig("locationID");
		const response = await apRequestV2<{ succeded: boolean }>({
			url: `/locations/${locationId}/customFields/${id}/`,
			method: "DELETE",
		});
		return response.succeded;
	},
};

/**
 * Appointment operations
 */
export const apAppointmentReq = {
	/**
	 * Get an appointment by ID
	 */
	get: async (
		apId: string,
		invalidateCache?: boolean,
	): Promise<GetAPAppointmentType> => {
		const response = await apRequestV2<{ appointment: GetAPAppointmentType }>({
			url: `/calendars/events/appointments/${apId}`,
			method: "GET",
			invalidateCache,
		});
		return response.appointment;
	},

	/**
	 * Create a new appointment
	 */
	post: async (
		payload: PostAPAppointmentType,
	): Promise<GetAPAppointmentType> => {
		const calendarId = getConfig("apCalendarId");
		const locationId = getConfig("locationID");
		return apRequestV2({
			url: "/calendars/events/appointments",
			method: "POST",
			data: { calendarId, ...payload, locationId },
		});
	},

	/**
	 * Create a block slot appointment
	 */
	postBlockSlot: async (
		payload: PostAPAppointmentType,
	): Promise<GetAPAppointmentType> => {
		const calendarId = getConfig("apCalendarId");
		const locationId = getConfig("locationID");
		return apRequestV2({
			url: "/calendars/events/block-slots",
			method: "POST",
			data: { calendarId, ...payload, locationId },
		});
	},

	/**
	 * Update an existing appointment
	 */
	put: async (
		apId: string,
		payload: PutAPAppointmentType,
	): Promise<AppointmentUpdateResponse> => {
		const calendarId = getConfig("apCalendarId");
		const locationId = getConfig("locationID");
		return apRequestV2<AppointmentUpdateResponse>({
			url: `/calendars/events/appointments/${apId}`,
			method: "PUT",
			data: { calendarId, ...payload, locationId },
		});
	},

	/**
	 * Delete an appointment
	 */
	delete: async (apId: string): Promise<boolean> => {
		const response = await apRequestV2<{ succeeded: boolean }>({
			url: `/calendars/events/${apId}`,
			method: "DELETE",
		});
		return response.succeeded;
	},
};

/**
 * Note operations
 */
export const apNoteReq = {
	/**
	 * Create a new note for a contact
	 */
	post: async (contactId: string, payload: string): Promise<GetAPNoteType> => {
		const response = await apRequestV2<{ note: GetAPNoteType }>({
			url: `/contacts/${contactId}/notes/`,
			method: "POST",
			data: {
				body: payload,
			},
		});
		return response.note;
	},

	/**
	 * Update an existing note
	 */
	put: async (
		contactId: string,
		noteId: string,
		payload: string,
	): Promise<GetAPNoteType> => {
		const response = await apRequestV2<{ note: GetAPNoteType }>({
			url: `/contacts/${contactId}/notes/${noteId}/`,
			method: "PUT",
			data: {
				body: payload,
			},
		});
		return response.note;
	},

	/**
	 * Delete a note
	 */
	delete: async (contactId: string, noteId: string): Promise<boolean> => {
		const response = await apRequestV2<{ succeded: boolean }>({
			url: `/contacts/${contactId}/notes/${noteId}/`,
			method: "DELETE",
		});
		return response.succeded;
	},
};
