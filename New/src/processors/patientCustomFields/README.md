# Patient Custom Field Value Synchronization

This module provides comprehensive patient custom field value synchronization between AutoPatient (AP) and CliniCore (CC) platforms. It handles bidirectional value conversion and synchronization with proper error handling and logging.

## Overview

The system synchronizes patient custom field **values** (not field definitions) between platforms using existing field mappings stored in the database. It converts values between different field types and handles platform-specific API requirements.

## Key Features

- **Bidirectional Synchronization**: AP ↔ CC value synchronization
- **Type-Aware Conversion**: Handles different field types (text, select, boolean, date, etc.)
- **Error Handling**: Comprehensive error handling with detailed logging
- **Field Mapping**: Uses existing field mappings from database
- **API Compatibility**: Follows proven v3Integration API patterns

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   AP Platform   │    │  Sync Engine     │    │  CC Platform    │
│                 │    │                  │    │                 │
│ Contact Data    │◄──►│ Value Converter  │◄──►│ Patient Data    │
│ Custom Fields   │    │ Field Mappings   │    │ Custom Fields   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Core Components

### 1. Main Sync Functions (`index.ts`)
- `syncApToCcCustomFields()` - Sync AP → CC
- `syncCcToApCustomFields()` - Sync CC → AP

### 2. Value Converters (`valueConverters.ts`)
- `convertApValueToCc()` - Convert AP values to CC format
- `convertCcValueToAp()` - Convert CC values to AP format

### 3. Patient Data Fetcher (`patientDataFetcher.ts`)
- `fetchApPatientData()` - Fetch patient data from AP
- `fetchCcPatientData()` - Fetch patient data from CC
- `updateApPatientCustomFields()` - Update AP patient values
- `updateCcPatientCustomFields()` - Update CC patient values

### 4. Field Mapping Resolver (`fieldMappingResolver.ts`)
- `getAllFieldMappings()` - Get all field mappings from database
- `getFieldMappingByApId()` - Get mapping by AP field ID
- `getFieldMappingByCcId()` - Get mapping by CC field ID

## API Usage

### Endpoint
```
POST /admin/custom-fields-sync/:id/:platform
```

### Parameters
- `:id` - Local database patient ID
- `:platform` - Target platform ("ap" or "cc")

### Query Parameters
- `skip=true` - Skip fields without mappings (optional)

### Examples

**Sync AP → CC:**
```bash
curl -X POST "https://your-domain.com/admin/custom-fields-sync/patient-123/cc?skip=true"
```

**Sync CC → AP:**
```bash
curl -X POST "https://your-domain.com/admin/custom-fields-sync/patient-123/ap?skip=true"
```

## Value Conversion Logic

### AP → CC Conversion
```typescript
// AP Format: {id: "field123", value: "some value"}
// CC Format: {field: {...}, values: [{value: "some value"}], patient: null}
```

### CC → AP Conversion
```typescript
// CC Format: {field: {...}, values: [{value: "some value"}], patient: 123}
// AP Format: {id: "field123", value: "some value"}
```

### Field Type Handling

| AP Type | CC Type | Conversion Notes |
|---------|---------|------------------|
| TEXT | text | Direct string conversion |
| LARGE_TEXT | textarea | Direct string conversion |
| NUMERICAL | number | Number validation |
| PHONE | telephone | String conversion |
| MONETORY | text | String conversion |
| CHECKBOX | select (multi) | Array to comma-separated |
| SINGLE_OPTIONS | select | Direct value |
| MULTIPLE_OPTIONS | select (multi) | Array to comma-separated |
| DATE | date | ISO date format |
| RADIO | select/boolean | Value matching |
| EMAIL | email | String conversion |
| TEXTBOX_LIST | text (multi) | Array to comma-separated |

## Error Handling

The system provides comprehensive error handling:

1. **Missing Field Mappings**: Skip or fail based on `skipMissingFields` option
2. **Value Conversion Errors**: Log errors and continue with other fields
3. **API Errors**: Detailed error logging with request correlation
4. **Database Errors**: Proper error propagation and logging

## Response Format

```typescript
{
  success: boolean,
  patientId: string,
  targetPlatform: "ap" | "cc",
  fieldsProcessed: number,
  fieldsUpdated: number,
  fieldsSkipped: number,
  fieldsFailed: number,
  results: FieldValueSyncResult[],
  errors: string[],
  warnings: string[],
  executionTimeMs: number
}
```

## Database Requirements

The system requires:
1. **Patient Table**: With `apId` and `ccId` columns
2. **Custom Fields Table**: With field mappings (`apId`, `ccId`, `apConfig`, `ccConfig`)

## Integration with Existing System

This module integrates seamlessly with:
- Existing admin endpoint (`/admin/custom-fields-sync/:id/:platform`)
- Existing field mapping system
- Existing API clients (`apiClient.ap` and `apiClient.cc`)
- Existing logging and error handling utilities

## Logging

All operations are logged with request ID correlation:
- Debug: Detailed conversion steps
- Info: Operation summaries and statistics
- Warn: Non-critical issues (missing mappings, conversion warnings)
- Error: Critical failures and API errors

## Performance Considerations

- Batch field processing for efficiency
- Minimal database queries using lookup tables
- Proper error isolation (one field failure doesn't stop others)
- Request ID correlation for debugging

## Future Enhancements

- Dry-run mode for testing
- Field value validation before API calls
- Conflict resolution for concurrent updates
- Metrics and monitoring integration
