/**
 * Custom Fields Conflict Detection Module
 *
 * Comprehensive conflict detection utilities for custom field synchronization
 * between AutoPatient (AP) and CliniCore (CC) platforms. Provides detection
 * for standard field conflicts, existing custom field conflicts, blocklist
 * violations, and type compatibility checking.
 *
 * @fileoverview Conflict detection module exports
 * @version 2.0.0
 * @since 2024-07-28
 */

// Re-export types for convenience
export type { FieldConflictResult, Platform } from "../types";

// Type compatibility and utility functions
export {
	areFieldTypesCompatible,
	generateUniqueFieldName,
} from "./compatibility";
// Core conflict detection functions
export {
	checkApFieldCreationBlocklist,
	checkForExistingCustomFieldConflict,
	checkForStandardFieldMapping,
	detectFieldConflicts,
} from "./detector";
