/**
 * Custom Fields Matching Module
 *
 * Intelligent field matching between AutoPatient and CliniCore custom fields
 * using normalization, similarity scoring, and conflict detection. Provides
 * comprehensive matching strategies and algorithms for field synchronization.
 *
 * @fileoverview Field matching module exports
 * @version 2.0.0
 * @since 2024-07-28
 */

// Re-export types for convenience
export type { CustomFieldMapping, FieldMatchResult, Platform } from "../types";

// Advanced matching algorithms
export {
	matchAPFieldWithCC,
	matchCCFieldWithAP,
} from "./algorithms";
// Core matching strategies
export {
	fieldsMatch,
	findExistingCustomField,
	findMatchingField,
} from "./strategies";
