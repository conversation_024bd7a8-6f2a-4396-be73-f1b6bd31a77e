/**
 * AutoPatient Field Conversion Utilities
 *
 * Provides specialized utilities for converting AutoPatient custom fields
 * to CliniCore format with comprehensive type mapping, value transformation,
 * and enhanced logging capabilities.
 *
 * @fileoverview AP-specific field conversion utilities
 * @version 2.0.0
 * @since 2024-07-28
 */

import type { APGetCustomFieldType, PostCCCustomField } from "@type";
import { logCustomField, logFieldConversionFailure } from "@/utils/logger";
import {
	type APFieldType,
	shouldConvertRadioToBoolean,
	shouldSkipApField,
} from "../config/fieldTypeMappings";

/**
 * Convert AutoPatient custom field to CliniCore format
 *
 * Performs intelligent field type conversion with bidirectional compatibility.
 * Handles edge cases gracefully and provides detailed logging for traceability.
 * Uses centralized mapping configuration for consistent conversions.
 *
 * @param apField - AutoPatient custom field object to convert
 * @returns CliniCore custom field format ready for API submission or null if should be skipped
 *
 * @example
 * ```typescript
 * // Convert AP TEXTBOX_LIST to CC multi-value text
 * const apTextboxField: APGetCustomFieldType = {
 *   id: "field123",
 *   name: "Patient Allergies",
 *   dataType: "TEXTBOX_LIST"
 * };
 *
 * const ccField = convertApFieldToCc(apTextboxField);
 * // Result: { type: "text", allowMultipleValues: true, ... }
 *
 * // FILE_UPLOAD fields are skipped
 * const apFileField: APGetCustomFieldType = {
 *   id: "field456",
 *   name: "Document Upload",
 *   dataType: "FILE_UPLOAD"
 * };
 *
 * const skippedField = convertApFieldToCc(apFileField);
 * // Result: null (field is skipped)
 * ```
 */
export function convertApFieldToCc(
	apField: APGetCustomFieldType,
): PostCCCustomField | null {
	const fieldType = apField.dataType as APFieldType;

	// Check if field should be skipped entirely
	if (shouldSkipApField(fieldType)) {
		logCustomField("AP→CC field skipped", apField.name, {
			apFieldType: fieldType,
			reason: "FILE_UPLOAD fields are not synced to CC",
		});
		return null;
	}

	logCustomField("AP→CC conversion started", apField.name, {
		apFieldType: fieldType,
		hasOptions: Boolean(apField.picklistOptions?.length),
		optionCount: apField.picklistOptions?.length || 0,
	});

	// Base field structure with common properties
	const baseField: PostCCCustomField = {
		name: apField.name,
		label: apField.name, // Use name as label by default
		type: "text", // Default fallback type
		validation: "{}",
		isRequired: false,
		allowMultipleValues: false,
	};

	// Use centralized mapping configuration
	switch (fieldType) {
		case "TEXT":
			return convertApTextField(apField, baseField);
		case "LARGE_TEXT":
			return convertApLargeTextField(apField, baseField);
		case "NUMERICAL":
			return convertApNumericalField(apField, baseField);
		case "PHONE":
			return convertApPhoneField(apField, baseField);
		case "MONETORY":
			return convertApMonetoryField(apField, baseField);
		case "CHECKBOX":
			return convertApCheckboxField(apField, baseField);
		case "SINGLE_OPTIONS":
			return convertApSingleOptionsField(apField, baseField);
		case "MULTIPLE_OPTIONS":
			return convertApMultipleOptionsField(apField, baseField);
		case "DATE":
			return convertApDateField(apField, baseField);
		case "RADIO":
			return convertApRadioField(apField, baseField);
		case "EMAIL":
			return convertApEmailField(apField, baseField);
		case "TEXTBOX_LIST":
			return convertApTextboxListField(apField, baseField);
		default:
			return convertApFallbackField(apField, baseField, fieldType);
	}
}

/**
 * Convert AP TEXT field to CC text
 */
function convertApTextField(
	apField: APGetCustomFieldType,
	baseField: PostCCCustomField,
): PostCCCustomField {
	logCustomField("AP→CC TEXT conversion", apField.name);
	return {
		...baseField,
		type: "text",
	};
}

/**
 * Convert AP LARGE_TEXT field to CC textarea
 */
function convertApLargeTextField(
	apField: APGetCustomFieldType,
	baseField: PostCCCustomField,
): PostCCCustomField {
	logCustomField("AP→CC LARGE_TEXT→textarea conversion", apField.name);
	return {
		...baseField,
		type: "textarea",
	};
}

/**
 * Convert AP NUMERICAL field to CC number
 */
function convertApNumericalField(
	apField: APGetCustomFieldType,
	baseField: PostCCCustomField,
): PostCCCustomField {
	logCustomField("AP→CC NUMERICAL→number conversion", apField.name);
	return {
		...baseField,
		type: "number",
	};
}

/**
 * Convert AP PHONE field to CC telephone
 */
function convertApPhoneField(
	apField: APGetCustomFieldType,
	baseField: PostCCCustomField,
): PostCCCustomField {
	logCustomField("AP→CC PHONE→telephone conversion", apField.name);
	return {
		...baseField,
		type: "telephone",
	};
}

/**
 * Convert AP MONETORY field to CC text
 */
function convertApMonetoryField(
	apField: APGetCustomFieldType,
	baseField: PostCCCustomField,
): PostCCCustomField {
	logCustomField("AP→CC MONETORY→text conversion", apField.name, {
		note: "Monetary values stored as text in CC",
	});
	return {
		...baseField,
		type: "text",
	};
}

/**
 * Convert AP CHECKBOX field to CC select with multiple values
 */
function convertApCheckboxField(
	apField: APGetCustomFieldType,
	baseField: PostCCCustomField,
): PostCCCustomField {
	const options = apField.picklistOptions || [];
	logCustomField("AP→CC CHECKBOX→select conversion", apField.name, {
		allowMultiple: true,
		optionCount: options.length,
	});

	// Handle both string[] and object[] formats
	const allowedValues = options.map((option) => {
		if (typeof option === "string") {
			return { value: option };
		} else {
			return { value: option.label };
		}
	});

	return {
		...baseField,
		type: "select",
		allowMultipleValues: true,
		allowedValues,
	};
}

/**
 * Convert AP DATE field to CC date
 */
function convertApDateField(
	apField: APGetCustomFieldType,
	baseField: PostCCCustomField,
): PostCCCustomField {
	logCustomField("AP→CC DATE→date conversion", apField.name);
	return {
		...baseField,
		type: "date",
	};
}

/**
 * Convert AP EMAIL field to CC email
 */
function convertApEmailField(
	apField: APGetCustomFieldType,
	baseField: PostCCCustomField,
): PostCCCustomField {
	logCustomField("AP→CC EMAIL→email conversion", apField.name);
	return {
		...baseField,
		type: "email",
	};
}

/**
 * Convert AP TEXTBOX_LIST field to CC multi-value text
 */
function convertApTextboxListField(
	apField: APGetCustomFieldType,
	baseField: PostCCCustomField,
): PostCCCustomField {
	logCustomField("AP→CC TEXTBOX_LIST→text(multi) conversion", apField.name, {
		allowMultiple: true,
		note: "Multi-value text field",
	});
	return {
		...baseField,
		type: "text",
		allowMultipleValues: true,
	};
}

/**
 * Convert AP RADIO field with intelligent boolean detection
 */
function convertApRadioField(
	apField: APGetCustomFieldType,
	baseField: PostCCCustomField,
): PostCCCustomField {
	const options = apField.picklistOptions || [];

	// Convert options to string array for boolean check
	const stringOptions = options.map((option) => {
		if (typeof option === "string") {
			return option;
		} else {
			return option.label;
		}
	});

	// Check if this should be converted to boolean (Yes/Ja, No/Nein pattern)
	if (shouldConvertRadioToBoolean(stringOptions)) {
		logCustomField("AP→CC RADIO→boolean conversion", apField.name, {
			detectedAsBoolean: true,
			originalOptions: options,
			conversionReason: "yes_no_pattern_detected",
		});

		return {
			...baseField,
			type: "boolean",
		};
	}

	// Convert to select field for other radio fields
	logCustomField("AP→CC RADIO→select conversion", apField.name, {
		optionCount: options.length,
		options: options,
		conversionReason:
			options.length === 0 ? "no_options" : "non_boolean_pattern",
	});

	// Handle both string[] and object[] formats for allowedValues
	const allowedValues = options.map((option) => {
		if (typeof option === "string") {
			return { value: option };
		} else {
			return { value: option.label };
		}
	});

	return {
		...baseField,
		type: "select",
		allowMultipleValues: false,
		allowedValues,
	};
}

/**
 * Convert AP MULTIPLE_OPTIONS field to CC select with multiple values
 */
function convertApMultipleOptionsField(
	apField: APGetCustomFieldType,
	baseField: PostCCCustomField,
): PostCCCustomField {
	const options = apField.picklistOptions || [];

	logCustomField("AP→CC MULTIPLE_OPTIONS→select conversion", apField.name, {
		optionCount: options.length,
		allowMultiple: true,
	});

	// Handle both string[] and object[] formats
	const allowedValues = options.map((option) => {
		if (typeof option === "string") {
			return { value: option };
		} else {
			return { value: option.label };
		}
	});

	return {
		...baseField,
		type: "select",
		allowMultipleValues: true,
		allowedValues,
	};
}

/**
 * Convert AP SINGLE_OPTIONS field to CC select
 */
function convertApSingleOptionsField(
	apField: APGetCustomFieldType,
	baseField: PostCCCustomField,
): PostCCCustomField {
	const options = apField.picklistOptions || [];

	logCustomField("AP→CC SINGLE_OPTIONS→select conversion", apField.name, {
		optionCount: options.length,
		allowMultiple: false,
	});

	// Handle both string[] and object[] formats
	const allowedValues = options.map((option) => {
		if (typeof option === "string") {
			return { value: option };
		} else {
			return { value: option.label };
		}
	});

	return {
		...baseField,
		type: "select",
		allowMultipleValues: false,
		allowedValues,
	};
}

/**
 * Convert unmapped AP field types with fallback to text
 */
function convertApFallbackField(
	apField: APGetCustomFieldType,
	baseField: PostCCCustomField,
	fieldType: APFieldType,
): PostCCCustomField {
	// Convert picklistOptions to string array for logging
	const picklistOptionsForLogging = apField.picklistOptions?.map((option) => {
		if (typeof option === "string") {
			return option;
		} else {
			return option.label;
		}
	});

	logFieldConversionFailure(
		"AP→CC",
		{
			id: apField.id,
			name: apField.name,
			type: fieldType,
			fieldKey: apField.fieldKey,
			picklistOptions: picklistOptionsForLogging,
		},
		{
			id: "fallback",
			name: "text_fallback",
			type: "text",
		},
		null,
		`Unsupported AP→CC conversion: ${fieldType} → text (using fallback)`,
	);

	return {
		...baseField,
		type: "text",
	};
}
