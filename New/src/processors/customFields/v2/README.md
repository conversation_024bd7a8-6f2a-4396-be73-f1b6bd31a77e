# Custom Field Synchronization v2

## 🎯 Overview

The v2 custom field synchronization system is a complete rewrite of the custom field sync functionality, designed to fix critical issues and provide a clean, maintainable architecture.

## 🔥 Problems Solved

1. **Email Sync Failure** - AP standard fields (email/phone) now properly sync to CC custom fields
2. **TEXTBOX_LIST Chaos** - Consistent Record<string, string> vs comma-separated handling
3. **Missing Pipe Separator** - Multi-value to TEXT conversion now uses ` | ` separator
4. **Fake Type Checking** - Real field type compatibility validation
5. **No Standard Field Architecture** - Proper standard-to-custom field mapping

## 📁 Architecture

```
New/src/processors/customFields/v2/
├── core/                    # Core functionality
│   ├── fieldMatcher.ts     # Intelligent field matching with real type checking
│   ├── valueConverter.ts   # Unified value conversion with proper separators
│   └── standardFieldMapper.ts # Standard field → Custom field mapping
├── config/                  # Configuration
│   ├── fieldMappings.ts    # Field type mapping rules
│   └── standardMappings.ts # Standard field mapping configuration
├── types/                   # Type definitions
│   └── index.ts            # Comprehensive type definitions
├── verify.ts               # Verification script
├── index.ts                # Main entry point
└── README.md               # This file
```

## 🔧 Core Components

### FieldMatcher
- **Exact matching**: Direct name/label comparison
- **Normalized matching**: German umlauts, spaces, special chars
- **Fuzzy matching**: Levenshtein distance with configurable threshold
- **Real type compatibility**: Actual validation based on field types

### ValueConverter
- **Unified conversion logic**: Single function for all field types
- **Proper separators**: ` | ` for multi-value → TEXT, `,` for TEXTBOX_LIST
- **Record<string, string> handling**: Consistent TEXTBOX_LIST processing
- **Type-aware conversion**: Respects source/target field types
- **Empty value filtering**: Removes empty strings automatically

### StandardFieldMapper
- **AP standard → CC custom**: email, phone mapping
- **CC standard → AP custom**: PatientID, CC Profile link
- **Configurable mappings**: Easy to add new standard field mappings
- **Value extraction**: JSONPath-based field extraction with fallbacks

## 🚀 Usage

### Basic Field Matching
```typescript
import { FieldMatcher } from './core/fieldMatcher.js';

const matcher = new FieldMatcher({
  strategy: FieldMatchStrategy.NORMALIZED,
  fuzzyThreshold: 0.8,
  normalizeGermanChars: true,
});

const result = matcher.findBestMatch(apField, ccFields);
```

### Value Conversion
```typescript
import { ValueConverter } from './core/valueConverter.js';

const converter = new ValueConverter();
const result = converter.convertValue({
  sourceType: "TEXTBOX_LIST",
  targetType: "text",
  sourceValue: { "0": "value1", "1": "value2" },
  isMultiValue: true,
});
// Result: "value1 | value2"
```

### Standard Field Mapping
```typescript
import { StandardFieldMapper } from './core/standardFieldMapper.js';

const mapper = new StandardFieldMapper();
const extractions = mapper.extractStandardFields(patientData, "ap");
const mappings = mapper.createStandardFieldMappings(extractions, "cc", existingFields);
```

### Convenience Functions
```typescript
import { matchFields, convertValue, extractStandardFields } from './index.js';

// Quick field matching
const match = matchFields(apField, ccFields);

// Quick value conversion
const converted = convertValue({
  sourceType: "EMAIL",
  targetType: "email",
  sourceValue: "<EMAIL>",
});

// Quick standard field extraction
const standards = extractStandardFields(patientData, "ap");
```

## 🔄 Value Conversion Examples

### TEXTBOX_LIST → Multi-value Text
```typescript
// Input: { "0": "phone1", "1": "phone2", "2": "phone3" }
// Output: "phone1 | phone2 | phone3"
```

### Multi-value Text → TEXTBOX_LIST
```typescript
// Input: "value1 | value2 | value3"
// Output: { "0": "value1", "1": "value2", "2": "value3" }
```

### RADIO → Boolean
```typescript
// Input: "Yes" / "Ja" → true
// Input: "No" / "Nein" → false
```

### Empty String Filtering
```typescript
// Input: ""
// Output: null (with warning)
```

## 🎛️ Configuration

### Field Type Mappings
Defined in `config/fieldMappings.ts`:
- AP → CC type mappings
- CC → AP type mappings
- Multi-value handling rules
- TEXTBOX_LIST compatibility

### Standard Field Mappings
Defined in `config/standardMappings.ts`:
- Field extraction paths
- Validation rules
- Transformation functions
- Default field names

## 🧪 Testing

Run the verification script to test core functionality:

```bash
# From the v2 directory
node verify.ts
```

The verification script tests:
- Field matching (exact, normalized, fuzzy)
- Value conversion (all major types)
- Type compatibility checking
- Standard field extraction

## 🚀 Migration Strategy

The v2 system is designed for gradual rollout using feature flags:

```typescript
// Environment variables
USE_V2_CUSTOM_FIELDS=true           # Enable full v2 system
ENABLE_V2_FIELD_MATCHING=true       # Enable v2 field matching only
ENABLE_V2_VALUE_CONVERSION=true     # Enable v2 value conversion only
ENABLE_V2_STANDARD_FIELDS=true      # Enable v2 standard field mapping only
```

## 📋 Implementation Status

### ✅ Phase 1: Core Infrastructure (COMPLETE)
- [x] Directory structure
- [x] Type definitions
- [x] Field matcher with real type checking
- [x] Unified value converter
- [x] Standard field mapper

### 🔄 Phase 2: Standard Field Support (NEXT)
- [ ] Email/phone → custom field logic
- [ ] CC ID → AP custom field logic
- [ ] Standard field synchronization testing

### 🔄 Phase 3: Synchronization Engines (PLANNED)
- [ ] Field definition sync
- [ ] Field value sync
- [ ] Standard field sync integration
- [ ] Error handling and logging

### 🔄 Phase 4: Integration & Testing (PLANNED)
- [ ] Feature flag implementation
- [ ] Update `/cf` route
- [ ] Update patient sync endpoints
- [ ] Integration testing

### 🔄 Phase 5: Migration & Cleanup (PLANNED)
- [ ] Migrate all endpoints
- [ ] Remove old code
- [ ] Documentation updates
- [ ] Performance optimization

## 🎉 Key Improvements

1. **Real Type Checking** - No more fake `return true` validations
2. **Proper Separators** - ` | ` for multi-value, `,` for TEXTBOX_LIST
3. **Standard Field Support** - Email/phone sync finally works
4. **Clean Architecture** - Modular, testable components
5. **Comprehensive Types** - Strong TypeScript typing throughout
6. **Empty Value Handling** - Automatic filtering of useless empty strings
7. **German Character Support** - Proper normalization for umlauts
8. **Fuzzy Matching** - Intelligent similarity-based matching
9. **Error Handling** - Structured error types and detailed feedback
10. **Migration Ready** - Feature flags for gradual rollout

## 🔗 Related Files

- `custom-field-sync.md` - Original rewrite plan
- `New/src/processors/customFields/` - Old v1 system (to be replaced)
- `DATA-TYPE-MAP.md` - Field type compatibility rules
