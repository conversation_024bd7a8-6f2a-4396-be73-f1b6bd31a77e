/**
 * Custom Field Synchronization v2 - Main Entry Point
 *
 * Clean, modular entry point for the v2 custom field synchronization system.
 * Provides a unified API for field matching, value conversion, and synchronization.
 *
 * @fileoverview v2 Custom field synchronization main entry point
 * @version 2.0.0
 * @since 2024-08-07
 */

// Core components
export { FieldMatcher } from "./core/fieldMatcher.js";
export { ValueConverter } from "./core/valueConverter.js";
export { StandardFieldMapper } from "./core/standardFieldMapper.js";

// Configuration
export * from "./config/fieldMappings.js";
export * from "./config/standardMappings.js";

// Types
export * from "./types/index.js";

// Re-export commonly used functions for convenience
import { FieldMatcher } from "./core/fieldMatcher.js";
import { ValueConverter } from "./core/valueConverter.js";
import { StandardFieldMapper } from "./core/standardFieldMapper.js";
import type {
	FieldMatchConfig,
	ValueConversionContext,
	SyncOptions,
	FieldMatchResult,
	ValueConversionResult,
	StandardFieldMappingResult,
} from "./types/index.js";

/**
 * Default instances for common usage
 */
const defaultFieldMatcher = new FieldMatcher();
const defaultValueConverter = new ValueConverter();
const defaultStandardFieldMapper = new StandardFieldMapper();

/**
 * Convenience function for field matching
 */
export function matchFields(
	apField: import("@/type/APTypes").APGetCustomFieldType,
	ccFields: import("@/type/CCTypes").GetCCCustomField[],
	config?: Partial<FieldMatchConfig>,
): FieldMatchResult {
	const matcher = config ? new FieldMatcher(config) : defaultFieldMatcher;
	return matcher.findBestMatch(apField, ccFields);
}

/**
 * Convenience function for value conversion
 */
export function convertValue(context: ValueConversionContext): ValueConversionResult {
	return defaultValueConverter.convertValue(context);
}

/**
 * Convenience function for standard field extraction
 */
export function extractStandardFields(
	patientData: Record<string, unknown>,
	sourcePlatform: "ap" | "cc",
) {
	return defaultStandardFieldMapper.extractStandardFields(patientData, sourcePlatform);
}

/**
 * Convenience function for creating standard field mappings
 */
export function createStandardFieldMappings(
	extractions: ReturnType<typeof extractStandardFields>,
	targetPlatform: "ap" | "cc",
	existingFields: (import("@/type/APTypes").APGetCustomFieldType | import("@/type/CCTypes").GetCCCustomField)[],
): StandardFieldMappingResult {
	return defaultStandardFieldMapper.createStandardFieldMappings(
		extractions,
		targetPlatform,
		existingFields,
	);
}

/**
 * Main v2 synchronization API
 */
export class CustomFieldSyncV2 {
	private fieldMatcher: FieldMatcher;
	private valueConverter: ValueConverter;
	private standardFieldMapper: StandardFieldMapper;

	constructor(config?: {
		fieldMatchConfig?: Partial<FieldMatchConfig>;
	}) {
		this.fieldMatcher = new FieldMatcher(config?.fieldMatchConfig);
		this.valueConverter = new ValueConverter();
		this.standardFieldMapper = new StandardFieldMapper();
	}

	/**
	 * Get field matcher instance
	 */
	public getFieldMatcher(): FieldMatcher {
		return this.fieldMatcher;
	}

	/**
	 * Get value converter instance
	 */
	public getValueConverter(): ValueConverter {
		return this.valueConverter;
	}

	/**
	 * Get standard field mapper instance
	 */
	public getStandardFieldMapper(): StandardFieldMapper {
		return this.standardFieldMapper;
	}

	/**
	 * Perform complete field synchronization
	 */
	public async synchronizeFields(
		apFields: import("@/type/APTypes").APGetCustomFieldType[],
		ccFields: import("@/type/CCTypes").GetCCCustomField[],
		options: SyncOptions,
	) {
		// This will be implemented in Phase 3: Synchronization Engines
		throw new Error("Full synchronization not yet implemented - Phase 3");
	}

	/**
	 * Synchronize patient field values
	 */
	public async synchronizePatientValues(
		patientData: Record<string, unknown>,
		fieldMappings: import("./types/index.js").FieldMapping[],
		options: SyncOptions,
	) {
		// This will be implemented in Phase 3: Synchronization Engines
		throw new Error("Patient value synchronization not yet implemented - Phase 3");
	}
}

/**
 * Create a new v2 synchronization instance
 */
export function createCustomFieldSyncV2(config?: {
	fieldMatchConfig?: Partial<FieldMatchConfig>;
}): CustomFieldSyncV2 {
	return new CustomFieldSyncV2(config);
}

/**
 * Version information
 */
export const VERSION = "2.0.0";
export const BUILD_DATE = "2024-08-07";

/**
 * Feature flags for gradual rollout
 */
export const FEATURE_FLAGS = {
	ENABLE_V2_FIELD_MATCHING: process.env.ENABLE_V2_FIELD_MATCHING === "true",
	ENABLE_V2_VALUE_CONVERSION: process.env.ENABLE_V2_VALUE_CONVERSION === "true",
	ENABLE_V2_STANDARD_FIELDS: process.env.ENABLE_V2_STANDARD_FIELDS === "true",
	ENABLE_V2_FULL_SYNC: process.env.ENABLE_V2_FULL_SYNC === "true",
} as const;

/**
 * Migration helper to check if v2 should be used
 */
export function shouldUseV2(): boolean {
	return process.env.USE_V2_CUSTOM_FIELDS === "true";
}

/**
 * Migration helper for gradual feature rollout
 */
export function isFeatureEnabled(feature: keyof typeof FEATURE_FLAGS): boolean {
	return FEATURE_FLAGS[feature];
}
