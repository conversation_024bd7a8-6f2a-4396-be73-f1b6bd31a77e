/**
 * Standard Field Mapper v2
 *
 * Handles mapping of standard platform fields to custom fields.
 * Supports AP standard fields (email, phone) → CC custom fields and
 * CC standard fields (patientId, profile links) → AP custom fields.
 *
 * @fileoverview v2 Standard field to custom field mapping
 * @version 2.0.0
 * @since 2024-08-07
 */

import type { APGetCustomFieldType } from "@/type/APTypes.js";
import type { GetCCCustomField } from "@/type/CCTypes.js";
import type {
	StandardFieldMapping,
	StandardFieldType,
	Platform,
	ValueSyncResult,
	SyncError,
	SyncErrorType,
} from "../types/index.js";
import {
	getStandardFieldExtractor,
	getDefaultStandardMapping,
	extractStandardFieldValue,
	validateStandardFieldValue,
	transformStandardFieldValue,
	generateStandardFieldName,
} from "../config/standardMappings.js";
import { ValueConverter } from "./valueConverter.js";

/**
 * Standard field extraction result
 */
export interface StandardFieldExtractionResult {
	fieldType: StandardFieldType;
	value: unknown;
	isValid: boolean;
	transformedValue: unknown;
	error?: string;
}

/**
 * Standard field mapping result
 */
export interface StandardFieldMappingResult {
	success: boolean;
	mappings: StandardFieldMapping[];
	extractions: StandardFieldExtractionResult[];
	errors: SyncError[];
	warnings: string[];
}

/**
 * Standard Field Mapper class
 */
export class StandardFieldMapper {
	private valueConverter: ValueConverter;

	constructor() {
		this.valueConverter = new ValueConverter();
	}

	/**
	 * Extract standard fields from patient data for a specific platform
	 */
	public extractStandardFields(
		patientData: Record<string, unknown>,
		sourcePlatform: Platform,
	): StandardFieldExtractionResult[] {
		const results: StandardFieldExtractionResult[] = [];
		
		// Get extractors for the source platform
		const extractors = sourcePlatform === "ap" 
			? ["email", "phone"] as StandardFieldType[]
			: ["patientId", "ccProfileLink"] as StandardFieldType[];

		for (const fieldType of extractors) {
			const extractor = getStandardFieldExtractor(fieldType, sourcePlatform);
			if (!extractor) {
				continue;
			}

			try {
				// Extract the value
				const value = extractStandardFieldValue(patientData, extractor);
				
				// Validate the value
				const isValid = validateStandardFieldValue(value, fieldType);
				
				// Transform the value if valid
				const transformedValue = isValid 
					? transformStandardFieldValue(value, fieldType)
					: null;

				results.push({
					fieldType,
					value,
					isValid,
					transformedValue,
					error: !isValid ? `Invalid ${fieldType} value: ${value}` : undefined,
				});
			} catch (error) {
				results.push({
					fieldType,
					value: null,
					isValid: false,
					transformedValue: null,
					error: error instanceof Error ? error.message : `Failed to extract ${fieldType}`,
				});
			}
		}

		return results;
	}

	/**
	 * Create standard field mappings for extracted fields
	 */
	public createStandardFieldMappings(
		extractions: StandardFieldExtractionResult[],
		targetPlatform: Platform,
		existingFields: (APGetCustomFieldType | GetCCCustomField)[],
	): StandardFieldMappingResult {
		const mappings: StandardFieldMapping[] = [];
		const errors: SyncError[] = [];
		const warnings: string[] = [];

		for (const extraction of extractions) {
			if (!extraction.isValid || extraction.transformedValue === null) {
				warnings.push(`Skipping invalid ${extraction.fieldType} field`);
				continue;
			}

			try {
				// Get default mapping configuration
				const defaultMapping = getDefaultStandardMapping(
					extraction.fieldType,
					targetPlatform,
				);

				if (!defaultMapping) {
					warnings.push(`No default mapping found for ${extraction.fieldType} → ${targetPlatform}`);
					continue;
				}

				// Check if field already exists
				const existingField = this.findExistingStandardField(
					extraction.fieldType,
					targetPlatform,
					existingFields,
				);

				if (existingField) {
					// Use existing field
					mappings.push({
						...defaultMapping,
						customFieldName: this.getFieldName(existingField),
					});
				} else {
					// Use default mapping (field will be created if needed)
					mappings.push(defaultMapping);
				}
			} catch (error) {
				errors.push({
					type: SyncErrorType.VALIDATION_ERROR,
					message: `Failed to create mapping for ${extraction.fieldType}`,
					field: extraction.fieldType,
					originalError: error instanceof Error ? error : undefined,
				});
			}
		}

		return {
			success: errors.length === 0,
			mappings,
			extractions,
			errors,
			warnings,
		};
	}

	/**
	 * Sync standard field values to custom fields
	 */
	public async syncStandardFieldValues(
		patientData: Record<string, unknown>,
		mappings: StandardFieldMapping[],
		requestId: string,
	): Promise<ValueSyncResult[]> {
		const results: ValueSyncResult[] = [];

		for (const mapping of mappings) {
			try {
				// Extract the value
				const extractor = getStandardFieldExtractor(
					mapping.standardField,
					mapping.platform === "ap" ? "cc" : "ap", // Source is opposite of target
				);

				if (!extractor) {
					results.push({
						fieldName: mapping.customFieldName,
						fieldType: mapping.customFieldType,
						success: false,
						action: "failed",
						isStandardField: true,
						error: `No extractor found for ${mapping.standardField}`,
					});
					continue;
				}

				const rawValue = extractStandardFieldValue(patientData, extractor);
				
				// Validate and transform
				if (!validateStandardFieldValue(rawValue, mapping.standardField)) {
					results.push({
						fieldName: mapping.customFieldName,
						fieldType: mapping.customFieldType,
						success: false,
						action: "skipped",
						originalValue: rawValue,
						isStandardField: true,
						warnings: [`Invalid ${mapping.standardField} value`],
					});
					continue;
				}

				const transformedValue = transformStandardFieldValue(rawValue, mapping.standardField);

				// Convert value if needed
				const conversionResult = this.valueConverter.convertValue({
					sourceType: mapping.platform === "ap" ? "text" : "TEXT", // Assume text for standard fields
					targetType: mapping.customFieldType,
					sourceValue: transformedValue,
				});

				if (!conversionResult.success) {
					results.push({
						fieldName: mapping.customFieldName,
						fieldType: mapping.customFieldType,
						success: false,
						action: "failed",
						originalValue: rawValue,
						isStandardField: true,
						error: conversionResult.error,
					});
					continue;
				}

				results.push({
					fieldName: mapping.customFieldName,
					fieldType: mapping.customFieldType,
					success: true,
					action: "updated",
					originalValue: rawValue,
					convertedValue: conversionResult.convertedValue,
					isStandardField: true,
					warnings: conversionResult.warnings,
				});
			} catch (error) {
				results.push({
					fieldName: mapping.customFieldName,
					fieldType: mapping.customFieldType,
					success: false,
					action: "failed",
					isStandardField: true,
					error: error instanceof Error ? error.message : "Unknown error",
				});
			}
		}

		return results;
	}

	/**
	 * Find existing standard field in target platform
	 */
	private findExistingStandardField(
		standardFieldType: StandardFieldType,
		targetPlatform: Platform,
		existingFields: (APGetCustomFieldType | GetCCCustomField)[],
	): APGetCustomFieldType | GetCCCustomField | null {
		const expectedName = generateStandardFieldName(standardFieldType, targetPlatform);
		
		return existingFields.find(field => {
			const fieldName = this.getFieldName(field);
			return fieldName.toLowerCase().includes(expectedName.toLowerCase()) ||
				   this.isStandardFieldPattern(fieldName, standardFieldType);
		}) || null;
	}

	/**
	 * Check if field name matches standard field pattern
	 */
	private isStandardFieldPattern(fieldName: string, standardFieldType: StandardFieldType): boolean {
		const normalizedName = fieldName.toLowerCase().trim();
		
		switch (standardFieldType) {
			case "email":
				return normalizedName.includes("ap") && normalizedName.includes("email");
			case "phone":
				return normalizedName.includes("ap") && 
					   (normalizedName.includes("phone") || normalizedName.includes("telefon"));
			case "patientId":
				return normalizedName.includes("cc") && 
					   (normalizedName.includes("patient") || normalizedName.includes("id"));
			case "ccProfileLink":
				return normalizedName.includes("cc") && 
					   (normalizedName.includes("profile") || normalizedName.includes("link"));
			default:
				return false;
		}
	}

	/**
	 * Get field name with fallback to label
	 */
	private getFieldName(field: APGetCustomFieldType | GetCCCustomField): string {
		if ('label' in field && field.label) {
			return field.label;
		}
		return field.name || "";
	}

	/**
	 * Generate field creation request for missing standard field
	 */
	public generateStandardFieldCreationRequest(
		mapping: StandardFieldMapping,
		targetPlatform: Platform,
	): Record<string, unknown> {
		const baseRequest = {
			name: mapping.customFieldName,
			label: mapping.customFieldName,
			type: mapping.customFieldType,
			required: mapping.required || false,
		};

		if (targetPlatform === "cc") {
			return {
				...baseRequest,
				allowMultipleValues: false,
				description: `Standard field mapping for ${mapping.standardField}`,
			};
		} else {
			return {
				...baseRequest,
				dataType: mapping.customFieldType,
				fieldKey: mapping.customFieldName.toLowerCase().replace(/\s+/g, "_"),
				description: `Standard field mapping for ${mapping.standardField}`,
			};
		}
	}
}
