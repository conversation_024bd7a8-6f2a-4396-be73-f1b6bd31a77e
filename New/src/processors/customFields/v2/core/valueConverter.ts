/**
 * Value Converter v2
 *
 * Unified value conversion with proper separators and type handling.
 * Handles conversion between AP and CC field values with support for
 * multi-value fields, TEXTBOX_LIST, and proper separator usage.
 *
 * @fileoverview v2 Unified value converter
 * @version 2.0.0
 * @since 2024-08-07
 */

import type {
	ValueConversionContext,
	ValueConversionResult,
	APFieldDataType,
	CCFieldType,
} from "../types/index.js";
import {
	MULTI_VALUE_CONFIG,
	TEXTBOX_LIST_CONFIG,
	BOOLEAN_RADIO_OPTIONS,
} from "../config/fieldMappings.js";

/**
 * Value Converter class with unified conversion logic
 */
export class ValueConverter {
	/**
	 * Convert value from source to target field type
	 */
	public convertValue(context: ValueConversionContext): ValueConversionResult {
		const { sourceType, targetType, sourceValue } = context;

		// Handle null/undefined values
		if (sourceValue === null || sourceValue === undefined) {
			return {
				success: true,
				convertedValue: null,
				originalValue: sourceValue,
				conversionType: "direct",
			};
		}

		// Handle empty string values (filter them out)
		if (sourceValue === "") {
			return {
				success: true,
				convertedValue: null,
				originalValue: sourceValue,
				conversionType: "direct",
				warnings: ["Empty string value filtered out"],
			};
		}

		try {
			// Determine conversion direction
			const isApToCC = this.isAPFieldType(sourceType);
			
			if (isApToCC) {
				return this.convertApToCC(
					sourceType as APFieldDataType,
					targetType as CCFieldType,
					sourceValue,
					context,
				);
			} else {
				return this.convertCCToAP(
					sourceType as CCFieldType,
					targetType as APFieldDataType,
					sourceValue,
					context,
				);
			}
		} catch (error) {
			return {
				success: false,
				convertedValue: null,
				originalValue: sourceValue,
				conversionType: "failed",
				error: error instanceof Error ? error.message : "Unknown conversion error",
			};
		}
	}

	/**
	 * Convert AP field value to CC field value
	 */
	private convertApToCC(
		sourceType: APFieldDataType,
		targetType: CCFieldType,
		sourceValue: unknown,
		context: ValueConversionContext,
	): ValueConversionResult {
		// Handle TEXTBOX_LIST → multi-value text conversion
		if (sourceType === "TEXTBOX_LIST" && targetType === "text") {
			return this.convertTextboxListToMultiText(sourceValue, context);
		}

		// Handle RADIO → boolean conversion
		if (sourceType === "RADIO" && targetType === "boolean") {
			return this.convertRadioToBoolean(sourceValue);
		}

		// Handle multi-option fields → select conversion
		if ((sourceType === "MULTIPLE_OPTIONS" || sourceType === "CHECKBOX") && targetType === "select") {
			return this.convertMultiOptionsToSelect(sourceValue, context);
		}

		// Handle single option fields → select conversion
		if ((sourceType === "SINGLE_OPTIONS" || sourceType === "RADIO") && targetType === "select") {
			return this.convertSingleOptionToSelect(sourceValue);
		}

		// Handle direct type conversions
		return this.convertDirectType(sourceValue, sourceType, targetType);
	}

	/**
	 * Convert CC field value to AP field value
	 */
	private convertCCToAP(
		sourceType: CCFieldType,
		targetType: APFieldDataType,
		sourceValue: unknown,
		context: ValueConversionContext,
	): ValueConversionResult {
		// Handle multi-value text → TEXTBOX_LIST conversion
		if (sourceType === "text" && targetType === "TEXTBOX_LIST" && context.isMultiValue) {
			return this.convertMultiTextToTextboxList(sourceValue, context);
		}

		// Handle boolean → RADIO conversion
		if (sourceType === "boolean" && targetType === "RADIO") {
			return this.convertBooleanToRadio(sourceValue);
		}

		// Handle select → options conversion
		if (sourceType === "select" && (targetType === "MULTIPLE_OPTIONS" || targetType === "SINGLE_OPTIONS")) {
			return this.convertSelectToOptions(sourceValue, targetType, context);
		}

		// Handle multi-value fields → TEXTBOX_LIST conversion
		if (context.isMultiValue && targetType === "TEXTBOX_LIST") {
			return this.convertMultiValueToTextboxList(sourceValue, sourceType);
		}

		// Handle direct type conversions
		return this.convertDirectType(sourceValue, sourceType, targetType);
	}

	/**
	 * Convert TEXTBOX_LIST to multi-value text with proper separator
	 */
	private convertTextboxListToMultiText(
		sourceValue: unknown,
		context: ValueConversionContext,
	): ValueConversionResult {
		if (typeof sourceValue !== "object" || sourceValue === null) {
			return {
				success: false,
				convertedValue: null,
				originalValue: sourceValue,
				conversionType: "failed",
				error: "TEXTBOX_LIST value must be an object",
			};
		}

		const record = sourceValue as Record<string, string>;
		const values = Object.values(record)
			.filter(value => value && value.trim() !== ""); // Filter out empty values

		if (values.length === 0) {
			return {
				success: true,
				convertedValue: null,
				originalValue: sourceValue,
				conversionType: "direct",
				warnings: ["No non-empty values found in TEXTBOX_LIST"],
			};
		}

		// Use pipe separator for multi-value text
		const convertedValue = values.join(MULTI_VALUE_CONFIG.textSeparator);

		return {
			success: true,
			convertedValue,
			originalValue: sourceValue,
			conversionType: "formatted",
		};
	}

	/**
	 * Convert multi-value text to TEXTBOX_LIST with proper format
	 */
	private convertMultiTextToTextboxList(
		sourceValue: unknown,
		context: ValueConversionContext,
	): ValueConversionResult {
		if (typeof sourceValue !== "string") {
			return {
				success: false,
				convertedValue: null,
				originalValue: sourceValue,
				conversionType: "failed",
				error: "Multi-value text must be a string",
			};
		}

		// Split by pipe separator and clean values
		const values = sourceValue
			.split(MULTI_VALUE_CONFIG.textSeparator)
			.map(value => value.trim())
			.filter(value => value !== "");

		if (values.length === 0) {
			return {
				success: true,
				convertedValue: {},
				originalValue: sourceValue,
				conversionType: "transformed",
				warnings: ["No valid values found after splitting"],
			};
		}

		// Convert to Record<string, string> format for TEXTBOX_LIST
		const convertedValue: Record<string, string> = {};
		values.forEach((value, index) => {
			convertedValue[index.toString()] = value;
		});

		return {
			success: true,
			convertedValue,
			originalValue: sourceValue,
			conversionType: "transformed",
		};
	}

	/**
	 * Convert multi-value field to TEXTBOX_LIST
	 */
	private convertMultiValueToTextboxList(
		sourceValue: unknown,
		sourceType: CCFieldType,
	): ValueConversionResult {
		let values: string[] = [];

		if (Array.isArray(sourceValue)) {
			values = sourceValue.map(v => String(v)).filter(v => v.trim() !== "");
		} else if (typeof sourceValue === "string") {
			// Try to split by common separators
			const separators = [MULTI_VALUE_CONFIG.textSeparator, ",", ";", "\n"];
			for (const separator of separators) {
				if (sourceValue.includes(separator)) {
					values = sourceValue.split(separator).map(v => v.trim()).filter(v => v !== "");
					break;
				}
			}
			if (values.length === 0) {
				values = [sourceValue.trim()].filter(v => v !== "");
			}
		} else {
			values = [String(sourceValue)].filter(v => v.trim() !== "");
		}

		if (values.length === 0) {
			return {
				success: true,
				convertedValue: {},
				originalValue: sourceValue,
				conversionType: "transformed",
				warnings: ["No valid values found for TEXTBOX_LIST conversion"],
			};
		}

		// Convert to Record<string, string> format
		const convertedValue: Record<string, string> = {};
		values.forEach((value, index) => {
			convertedValue[index.toString()] = value;
		});

		return {
			success: true,
			convertedValue,
			originalValue: sourceValue,
			conversionType: "transformed",
		};
	}

	/**
	 * Convert RADIO to boolean
	 */
	private convertRadioToBoolean(sourceValue: unknown): ValueConversionResult {
		if (typeof sourceValue !== "string") {
			return {
				success: false,
				convertedValue: null,
				originalValue: sourceValue,
				conversionType: "failed",
				error: "RADIO value must be a string",
			};
		}

		const normalizedValue = sourceValue.toLowerCase().trim();
		
		// Check for positive values
		if (["yes", "ja", "true", "1"].includes(normalizedValue)) {
			return {
				success: true,
				convertedValue: true,
				originalValue: sourceValue,
				conversionType: "transformed",
			};
		}

		// Check for negative values
		if (["no", "nein", "false", "0"].includes(normalizedValue)) {
			return {
				success: true,
				convertedValue: false,
				originalValue: sourceValue,
				conversionType: "transformed",
			};
		}

		return {
			success: false,
			convertedValue: null,
			originalValue: sourceValue,
			conversionType: "failed",
			error: `Cannot convert RADIO value "${sourceValue}" to boolean`,
		};
	}

	/**
	 * Convert boolean to RADIO
	 */
	private convertBooleanToRadio(sourceValue: unknown): ValueConversionResult {
		if (typeof sourceValue !== "boolean") {
			return {
				success: false,
				convertedValue: null,
				originalValue: sourceValue,
				conversionType: "failed",
				error: "Boolean value expected",
			};
		}

		const convertedValue = sourceValue ? "Yes" : "No";

		return {
			success: true,
			convertedValue,
			originalValue: sourceValue,
			conversionType: "transformed",
		};
	}

	/**
	 * Convert multi-options to select
	 */
	private convertMultiOptionsToSelect(
		sourceValue: unknown,
		context: ValueConversionContext,
	): ValueConversionResult {
		if (Array.isArray(sourceValue)) {
			return {
				success: true,
				convertedValue: sourceValue,
				originalValue: sourceValue,
				conversionType: "direct",
			};
		}

		if (typeof sourceValue === "string") {
			// Split by comma for multiple values
			const values = sourceValue.split(",").map(v => v.trim()).filter(v => v !== "");
			return {
				success: true,
				convertedValue: values,
				originalValue: sourceValue,
				conversionType: "transformed",
			};
		}

		return {
			success: true,
			convertedValue: [String(sourceValue)],
			originalValue: sourceValue,
			conversionType: "transformed",
		};
	}

	/**
	 * Convert single option to select
	 */
	private convertSingleOptionToSelect(sourceValue: unknown): ValueConversionResult {
		return {
			success: true,
			convertedValue: String(sourceValue),
			originalValue: sourceValue,
			conversionType: "direct",
		};
	}

	/**
	 * Convert select to options
	 */
	private convertSelectToOptions(
		sourceValue: unknown,
		targetType: APFieldDataType,
		context: ValueConversionContext,
	): ValueConversionResult {
		if (targetType === "MULTIPLE_OPTIONS") {
			if (Array.isArray(sourceValue)) {
				return {
					success: true,
					convertedValue: sourceValue,
					originalValue: sourceValue,
					conversionType: "direct",
				};
			}
			return {
				success: true,
				convertedValue: [String(sourceValue)],
				originalValue: sourceValue,
				conversionType: "transformed",
			};
		}

		// Single option
		if (Array.isArray(sourceValue)) {
			return {
				success: true,
				convertedValue: sourceValue[0] || "",
				originalValue: sourceValue,
				conversionType: "transformed",
				warnings: sourceValue.length > 1 ? ["Multiple values found, using first value"] : undefined,
			};
		}

		return {
			success: true,
			convertedValue: String(sourceValue),
			originalValue: sourceValue,
			conversionType: "direct",
		};
	}

	/**
	 * Direct type conversion for simple cases
	 */
	private convertDirectType(
		sourceValue: unknown,
		sourceType: string,
		targetType: string,
	): ValueConversionResult {
		// For most direct conversions, just pass the value through
		return {
			success: true,
			convertedValue: sourceValue,
			originalValue: sourceValue,
			conversionType: "direct",
		};
	}

	/**
	 * Check if field type is AP type
	 */
	private isAPFieldType(type: string): boolean {
		return type === type.toUpperCase();
	}
}
