### 1. Field Type Mapping Configuration

**AP to CC Mappings:**
- TEXT → text
- LARGE_TEXT → textarea  
- NUMERICAL → number
- PHONE → telephone
- MONETORY → text
- CHECKBOX → select (allowMultipleValues: true)
- SINGLE_OPTIONS → select (allowMultipleValues: false)
- MULTIPLE_OPTIONS → select (allowMultipleValues: true)
- DATE → date
- RADIO → select (allowMultipleValues: false)
- RADIO (Yes/Ja, No/Nein values) → boolean
- EMAIL → email
- TEXTBOX_LIST → text (allowMultipleValues: true)
- FILE_UPLOAD → Skip entirely (do not sync)

**CC to AP Mappings:**
- text → TEXT
- textarea → LARGE_TEXT
- select (allowMultipleValues: true) → MULTIPLE_OPTIONS
- select (allowMultipleValues: false) → SINGLE_OPTIONS
- boolean → RADIO (with Yes/Ja, No/Nein options)
- select-or-custom → SINGLE_OPTIONS
- text (allowMultipleValues: true) → TEXTBOX_LIST
- number → NUMERICAL
- number (allowMultipleValues: true) → TEXTBOX_LIST
- textarea (allowMultipleValues: true) → TEXTBOX_LIST
- telephone → PHONE
- telephone (allowMultipleValues: true) → TEXTBOX_LIST
- email → EMAIL
- email (allowMultipleValues: true) → TEXTBOX_LIST
- Any unmapped CC field types → TEXT (fallback)

### 2. Field Matching Logic
Implement intelligent field matching using:
- CC field: `name`, `label` properties
- AP field: `name`, `fieldKey` properties
- Normalize for comparison: remove special characters (German umlauts, spaces, etc.), convert to lowercase
- If names match but types are incompatible, create new AP field with modified fieldKey to avoid conflicts
- **Critical**: Only create custom fields in AP, never create new fields in CC, While sync AP to CC, if any field is missing in CC, skip that field without any noise

### 3. Special Mapping Rules
- **AP TEXTBOX_LIST to CC**: Match if CC field type is text/number/textarea/telephone/email with `allowMultipleValues: true`
- **CC multi-value fields to AP**: If CC field has `allowMultipleValues: true` but no matching AP TEXTBOX_LIST exists, create new TEXTBOX_LIST field in AP

### 4. Value Conversion System
Create bidirectional value conversion functions with clear directional separation:
- **AP → CC conversion functions**: Handle transforming AP field values to CC format
- **CC → AP conversion functions**: Handle transforming CC field values to AP format
- Implement reversible transformations maintaining data integrity
- Handle type-specific conversions (boolean ↔ radio, multi-select ↔ textbox_list, etc.)

### 5. File Organization Structure
Organize code under `New/src/processors/customFields/` with:
- `cc/` subdirectory for CC-specific functions
- `ap/` subdirectory for AP-specific functions
- Separate files for each major function group
- Comprehensive JSDoc documentation for all functions
- Use `requestId` from `New/src/utils/` for proper request tracing
