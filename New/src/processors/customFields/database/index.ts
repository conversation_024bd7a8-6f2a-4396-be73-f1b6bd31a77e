/**
 * Custom Fields Database Operations Module
 *
 * Comprehensive database operations for custom field synchronization
 * between AutoPatient (AP) and CliniCore (CC) platforms. Provides
 * field mapping storage, updates, bulk operations, and query utilities
 * with comprehensive error handling and logging.
 *
 * @fileoverview Database operations module exports
 * @version 2.0.0
 * @since 2024-07-28
 */

// Re-export types for convenience
export type { CustomFieldInsert, CustomFieldMapping } from "../types";
// Core database operations
export {
	bulkUpsertFieldMappings,
	storeMappingForCreatedFields,
	storeStandardFieldMapping,
	updateFieldMapping,
} from "./operations";
