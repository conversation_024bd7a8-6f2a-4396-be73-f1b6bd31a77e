/**
 * CliniCore Field Creation Utilities
 *
 * Specialized utilities for creating CliniCore custom fields from
 * AutoPatient field definitions. Handles CC-specific field creation
 * logic, validation, and error handling.
 *
 * @fileoverview CC-specific field creation utilities
 * @version 2.0.0
 * @since 2024-07-28
 */

import apiClient from "@apiClient";
import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import { logDebug, logError, logInfo, logWarn } from "@/utils/logger";
import { convertApFieldToCc } from "../ap/fieldConverter";
import { generateUniqueFieldName } from "../conflict/compatibility";
import { storeMappingForCreatedFields } from "../database/operations";
import type { FieldCreationResult } from "../types";

/**
 * Create CliniCore field from AutoPatient field
 *
 * Creates a new custom field in CliniCore based on an AutoPatient field
 * definition. Handles field conversion, conflict resolution, and database
 * mapping storage with comprehensive error handling.
 *
 * @param apField - AutoPatient field to create in CliniCore
 * @returns Promise resolving to created CC field or null if creation failed
 *
 * @example
 * ```typescript
 * const apField: APGetCustomFieldType = {
 *   id: "123",
 *   name: "patient_notes",
 *   dataType: "LARGE_TEXT"
 * };
 *
 * const ccField = await createCcFieldFromAp(apField);
 * if (ccField) {
 *   console.log(`Created CC field: ${ccField.name} (ID: ${ccField.id})`);
 * }
 * ```
 *
 * @since 2.0.0
 */
export async function createCcFieldFromAp(
	apField: APGetCustomFieldType,
): Promise<GetCCCustomField | null> {
	try {
		// Convert AP field to CC format
		const ccFieldData = convertApFieldToCc(apField);
		if (!ccFieldData) {
			logWarn("AP field conversion returned null, skipping creation", {
				apFieldId: apField.id,
				apFieldName: apField.name,
				apFieldType: apField.dataType,
			});
			return null;
		}

		// Attempt to create the field
		const createdField =
			await apiClient.cc.ccCustomfieldReq.create(ccFieldData);

		if (createdField) {
			// Single success log with essential information only
			logInfo("CC field created successfully", {
				apFieldId: apField.id,
				apFieldName: apField.name,
				ccFieldId: createdField.id,
				ccFieldName: createdField.name,
			});

			// Store the mapping in database
			await storeMappingForCreatedFields(apField, createdField);

			return createdField;
		} else {
			logError("CC field creation returned null", {
				apFieldId: apField.id,
				apFieldName: apField.name,
			});
			return null;
		}
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);

		// Extract detailed error information if available
		let errorDetails: Record<string, unknown> = {};
		if (error instanceof Error && "details" in error) {
			const enhancedError = error as Error & {
				details?: {
					status?: number;
					statusText?: string;
					responseData?: unknown;
					requestData?: unknown;
				};
			};
			errorDetails = {
				status: enhancedError.details?.status,
				statusText: enhancedError.details?.statusText,
				responseData: enhancedError.details?.responseData,
				requestData: enhancedError.details?.requestData,
			};
		}

		// Check if error is due to existing field conflict
		const isExistingFieldConflict =
			errorMessage.toLowerCase().includes("already exists") ||
			errorMessage.toLowerCase().includes("duplicate") ||
			errorMessage.toLowerCase().includes("conflict") ||
			errorMessage.toLowerCase().includes("name is already taken");

		if (isExistingFieldConflict) {
			logWarn("CC field creation failed due to existing field conflict", {
				apFieldId: apField.id,
				apFieldName: apField.name,
				error: errorMessage,
				errorDetails,
			});

			// Try to find the existing field
			const existingField = await findCcFieldByName(apField.name);
			if (existingField) {
				logInfo("Found existing CC field, creating mapping", {
					apFieldId: apField.id,
					existingCcFieldId: existingField.id,
					existingCcFieldName: existingField.name,
				});

				// Store mapping to existing field
				await storeMappingForCreatedFields(apField, existingField);
				return existingField;
			}
		}

		logError("Failed to create CC field from AP field", {
			apFieldId: apField.id,
			apFieldName: apField.name,
			error: errorMessage,
			errorDetails,
			apiErrorType: errorDetails.status
				? `HTTP_${errorDetails.status}`
				: "UNKNOWN",
		});

		return null;
	}
}

/**
 * Create CC field with conflict resolution
 *
 * Enhanced field creation function that handles name conflicts by
 * generating unique field names when necessary. Provides comprehensive
 * result tracking and error handling.
 *
 * @param apField - AutoPatient field to create in CliniCore
 * @param existingCcFields - Existing CC fields to check for conflicts
 * @returns Promise resolving to comprehensive creation result
 *
 * @example
 * ```typescript
 * const result = await createCcFieldWithConflictResolution(
 *   apField,
 *   existingCcFields
 * );
 *
 * if (result.success && result.field) {
 *   console.log(`Successfully created field: ${result.field.name}`);
 * } else {
 *   console.error(`Creation failed: ${result.error}`);
 * }
 * ```
 *
 * @since 2.0.0
 */
export async function createCcFieldWithConflictResolution(
	apField: APGetCustomFieldType,
	existingCcFields: GetCCCustomField[],
): Promise<FieldCreationResult> {
	try {
		// First try normal creation
		const createdField = await createCcFieldFromAp(apField);

		if (createdField) {
			return {
				success: true,
				field: createdField,
			};
		}

		// If creation failed, try with unique name
		const uniqueName = generateUniqueFieldName(
			apField.name,
			apField,
			"ap",
			existingCcFields,
		);

		logInfo("Retrying CC field creation with unique name", {
			apFieldId: apField.id,
			originalName: apField.name,
			uniqueName,
		});

		// Create modified AP field with unique name
		const modifiedApField: APGetCustomFieldType = {
			...apField,
			name: uniqueName,
		};

		const retryCreatedField = await createCcFieldFromAp(modifiedApField);

		if (retryCreatedField) {
			return {
				success: true,
				field: retryCreatedField,
			};
		} else {
			return {
				success: false,
				error: "Field creation failed even with unique name",
			};
		}
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);

		// Extract detailed error information if available
		let errorDetails: Record<string, unknown> = {};
		if (error instanceof Error && "details" in error) {
			const enhancedError = error as Error & {
				details?: {
					status?: number;
					statusText?: string;
					responseData?: unknown;
					requestData?: unknown;
				};
			};
			errorDetails = {
				status: enhancedError.details?.status,
				statusText: enhancedError.details?.statusText,
				responseData: enhancedError.details?.responseData,
				requestData: enhancedError.details?.requestData,
			};
		}

		const isExistingFieldConflict =
			errorMessage.toLowerCase().includes("already exists") ||
			errorMessage.toLowerCase().includes("duplicate") ||
			errorMessage.toLowerCase().includes("conflict");

		return {
			success: false,
			error: errorMessage,
			errorDetails,
			existingFieldConflict: isExistingFieldConflict,
		};
	}
}

/**
 * Find existing CC field by name
 *
 * Searches through all CC custom fields to find one with the specified name.
 * Used for conflict resolution and field mapping operations.
 *
 * @param fieldName - Name of the field to search for
 * @returns Promise resolving to the existing CC field or null if not found
 *
 * @example
 * ```typescript
 * const existingField = await findCcFieldByName("patient_notes", "req-123");
 * if (existingField) {
 *   console.log(`Found existing field: ${existingField.name} (ID: ${existingField.id})`);
 * }
 * ```
 *
 * @since 2.0.0
 */
export async function findCcFieldByName(
	fieldName: string,
): Promise<GetCCCustomField | null> {
	try {
		logDebug("Searching for CC field by name", {
			fieldName,
		});

		// Fetch all CC fields to search through them
		const allCcFields = await apiClient.cc.ccCustomfieldReq.all(true); // Invalidate cache

		// Find field with matching name or label
		const existingField = allCcFields.find(
			(field) => field.name === fieldName || field.label === fieldName,
		);

		if (existingField) {
			logDebug("Found existing CC field with matching name", {
				fieldName,
				existingFieldId: existingField.id,
				existingFieldName: existingField.name,
				existingFieldType: existingField.type,
			});
			return existingField;
		}

		logDebug("No existing CC field found with matching name", {
			fieldName,
			totalCcFields: allCcFields.length,
		});

		return null;
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : String(error);
		logError("Failed to search for CC field by name", {
			fieldName,
			error: errorMessage,
		});
		return null;
	}
}
