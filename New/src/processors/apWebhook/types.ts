/**
 * AutoPatient Webhook Processing Types
 *
 * Type definitions and interfaces for AutoPatient webhook event processing.
 * Provides comprehensive type safety for webhook payload handling, event
 * processing, contact synchronization, and response structures.
 *
 * @fileoverview Type definitions for AutoPatient webhook processing operations
 * @version 1.0.0
 * @since 2024-07-27
 */

import type { dbSchema } from "@database";
import type {
	APContactWebhookPayload,
	GetAPContactType,
	GetCCPatientType,
	PostCCPatientType,
} from "@type";

/**
 * AutoPatient webhook event types
 */
export type APWebhookEvent = "contact_created" | "contact_updated";

/**
 * AutoPatient contact creation webhook payload structure
 *
 * Represents the complete webhook payload received from AutoPatient for
 * contact creation events. Based on the sample payload structure.
 */
export interface APContactCreationWebhookPayload
	extends APContactWebhookPayload {
	/** Contact ID from AutoPatient */
	contact_id: string;
	/** Contact first name */
	first_name?: string;
	/** Contact last name */
	last_name?: string;
	/** Contact full name */
	full_name?: string;
	/** Contact email address */
	email?: string;
	/** Contact phone number */
	phone?: string;
	/** Contact tags (comma-separated string) */
	tags?: string;
	/** Contact country */
	country?: string;
	/** Contact creation timestamp */
	date_created: string;
	/** Contact full address */
	full_address?: string;
	/** Contact type (lead, customer, etc.) */
	contact_type?: string;
}

/**
 * Database patient record type
 */
export type PatientSelect = typeof dbSchema.patient.$inferSelect;

/**
 * Contact synchronization result
 *
 * Represents the outcome of synchronizing an AP contact to CliniCore.
 * Contains action taken, resulting entities, and any error information.
 */
export interface ContactSyncResult {
	/** Action performed during sync */
	action: "created" | "updated" | "skipped" | "failed";
	/** AutoPatient contact data */
	apContact?: GetAPContactType;
	/** CliniCore patient data (if created/updated) */
	ccPatient?: GetCCPatientType;
	/** Database patient record */
	dbPatient?: PatientSelect;
	/** Error message if sync failed */
	error?: string;
	/** Detailed error context */
	errorDetails?: {
		stage: string;
		originalError?: unknown;
	};
}

/**
 * Event processing context
 *
 * Contains configuration and metadata for processing webhook events.
 * Provides context for processors and enables customization of behavior.
 */
export interface EventProcessingContext {
	/** Processing configuration */
	config: WebhookProcessingConfig;
	/** Event start time for performance tracking */
	startTime: Date;
}

/**
 * Webhook processing configuration
 *
 * Configuration options for webhook event processing behavior.
 * Allows customization of sync logic and error handling.
 */
export interface WebhookProcessingConfig {
	/** Whether to skip sync buffer checks */
	skipSyncBuffer?: boolean;
	/** Custom sync buffer time in seconds */
	syncBufferSeconds?: number;
	/** Whether to create missing contacts in CC */
	createMissingContacts?: boolean;
	/** Whether to update existing contacts in CC */
	updateExistingContacts?: boolean;
	/** Webhook ID for lock creation (when called from queue) */
	webhookId?: string;
	/** Queue manager instance for lock creation */
	queueManager?: {
		createDuplicatePreventionLock: (options: {
			webhookId: string;
			targetPlatform: "cc" | "ap";
			patientId?: string;
			appointmentId?: string;
			sourceEntityId?: string;
			lockDurationSeconds?: number;
		}) => Promise<void>;
	};
}

/**
 * Event processing result
 *
 * Comprehensive result of webhook event processing including success/failure
 * status, processing metadata, and detailed sync information.
 */
export interface EventProcessingResult {
	/** Whether processing was successful */
	success: boolean;
	/** Event type that was processed */
	event: string;
	/** Contact ID from the webhook */
	contactId: string;
	/** Contact synchronization result */
	contactSync?: ContactSyncResult;
	/** Processing metadata */
	metadata: {
		/** Total processing duration in milliseconds */
		durationMs: number;
		/** Processing start timestamp */
		startTime: Date;
		/** Processing end timestamp */
		endTime: Date;
	};
	/** Error information if processing failed */
	error?: {
		/** Error message */
		message: string;
		/** Processing stage where error occurred */
		stage: string;
		/** Original error object */
		originalError?: unknown;
	};
}

/**
 * Contact lookup result
 *
 * Result of looking up existing patient records in the database.
 * Used to determine if contact already exists and needs updating.
 */
export interface ContactLookupResult {
	/** Existing patient record if found */
	patient?: PatientSelect;
	/** Whether patient was found */
	found: boolean;
	/** Lookup method used (apId, email, phone) */
	lookupMethod?: string;
}

/**
 * Field mapping result
 *
 * Result of mapping AP contact fields to CC patient format.
 * Contains mapped data and any mapping warnings or errors.
 */
export interface FieldMappingResult {
	/** Mapped CC patient data ready for API call */
	ccPatientData: PostCCPatientType;
	/** Mapping warnings (non-fatal issues) */
	warnings: string[];
	/** Successfully mapped field count */
	mappedFields: number;
	/** Fields that couldn't be mapped */
	unmappedFields: string[];
}
