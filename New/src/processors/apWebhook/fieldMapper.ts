/**
 * AutoPatient to CliniCore Field Mapping
 *
 * Provides field mapping utilities for converting AutoPatient contact data
 * to CliniCore patient format. Handles standard field mappings, custom
 * field processing, and data type conversions with comprehensive logging
 * and error handling.
 *
 * @fileoverview Field mapping utilities for AP to CC contact synchronization
 * @version 1.0.0
 * @since 2024-07-27
 */

import type { GetAPContactType, PostCCPatientType } from "@type";
import { logDebug, logError, logInfo, logWarn } from "@/utils/logger";
import type { FieldMappingResult } from "./types";

/**
 * Map AutoPatient contact data to CliniCore patient format
 *
 * Converts AutoPatient contact fields to CliniCore patient fields using
 * the standard field mapping configuration. Handles both direct field
 * mappings and custom field processing.
 * Now works with full contact data from AP API instead of webhook payload.
 *
 * **Mapping Strategy:**
 * 1. Map standard AP contact fields to CC patient fields
 * 2. Handle name field variations (firstName, lastName, fullName)
 * 3. Process address information from location data
 * 4. Identify custom fields that need separate processing
 * 5. Apply data type conversions and validation
 *
 * @param apContact - Full AutoPatient contact data from API
 * @returns Field mapping result with CC patient data and processing details
 *
 * @example
 * ```typescript
 * const mappingResult = mapApContactToCcPatient(apContact);
 * console.log(`Mapped ${mappingResult.mappedFields} standard fields`);
 *
 * // Use the mapped data for CC API call
 * const ccPatient = await apiClient.cc.createPatient(mappingResult.ccPatientData);
 * ```
 */
export async function mapApContactToCcPatient(
	apContact: GetAPContactType,
): Promise<FieldMappingResult> {
	logDebug(`Starting AP to CC field mapping for contact ID: ${apContact.id}`);

	const ccPatientData: Partial<PostCCPatientType> = {};
	const warnings: string[] = [];
	const unmappedFields: string[] = [];
	let mappedFields = 0;

	// Map name fields with priority handling
	mappedFields += mapNameFields(apContact, ccPatientData, warnings);

	// Map address fields from location data
	mappedFields += mapAddressFields(apContact, ccPatientData);

	// Map email and phone as custom fields (like v3Integration does)
	mappedFields += await mapEmailPhoneAsCustomFields(apContact, ccPatientData);

	// Process custom fields (from full contact data)
	processCustomFields(apContact, unmappedFields);

	// Note: custom fields are added directly to ccPatientData.customField

	return {
		ccPatientData: ccPatientData as PostCCPatientType,
		warnings,
		mappedFields,
		unmappedFields,
	};
}

/**
 * Map name fields with priority handling
 *
 * Handles name field mapping with priority: first_name/last_name > full_name.
 * Attempts to parse full_name if individual name fields are not available.
 *
 * @param apContact - AutoPatient contact data
 * @param ccPatientData - CliniCore patient data being built
 * @param warnings - Array to collect mapping warnings
 * @returns Number of fields successfully mapped
 */
function mapNameFields(
	apContact: GetAPContactType,
	ccPatientData: Partial<PostCCPatientType>,
	warnings: string[],
): number {
	let mappedCount = 0;

	// Priority 1: Use firstName and lastName if available
	if (apContact.firstName || apContact.lastName) {
		if (apContact.firstName) {
			ccPatientData.firstName = apContact.firstName;
			mappedCount++;
		}

		if (apContact.lastName) {
			ccPatientData.lastName = apContact.lastName;
			mappedCount++;
		}
	}
	// Priority 2: Parse name if individual names not available
	else if (apContact.name) {
		const nameParts = apContact.name.trim().split(/\s+/);
		if (nameParts.length >= 2) {
			ccPatientData.firstName = nameParts[0];
			ccPatientData.lastName = nameParts.slice(1).join(" ");
			mappedCount += 2;
		} else if (nameParts.length === 1) {
			ccPatientData.firstName = nameParts[0];
			mappedCount++;
		}
	} else {
		warnings.push("No name fields available in AP contact data");
		logWarn("No name fields found in contact data");
	}

	return mappedCount;
}

/**
 * Map address fields from location data
 *
 * Extracts address information from the AP contact's location object
 * and maps it to CC patient address format.
 *
 * @param apContact - AutoPatient contact data
 * @param ccPatientData - CliniCore patient data being built
 * @returns Number of fields successfully mapped
 */
function mapAddressFields(
	apContact: GetAPContactType,
	ccPatientData: Partial<PostCCPatientType>,
): number {
	let mappedCount = 0;

	// Map address fields from AP contact data
	const address: {
		id?: number;
		street?: string;
		city?: string;
		state?: string;
		zipCode?: string;
		country?: string;
		[key: string]: unknown;
	} = {};

	if (apContact.address1) {
		address.street = apContact.address1;
		mappedCount++;
	}

	if (apContact.city) {
		address.city = apContact.city;
		mappedCount++;
	}

	if (apContact.state) {
		address.state = apContact.state;
		mappedCount++;
	}

	if (apContact.postalCode) {
		address.zipCode = apContact.postalCode;
		mappedCount++;
	}

	if (apContact.country) {
		address.country = apContact.country;
		mappedCount++;
	}

	// Only add address if we have at least some address data
	if (mappedCount > 0) {
		// Add required fields for CC address format
		address.id = 1;
		address.label = null;
		address.name = null;
		address.streetNumber = "";
		address.postalCode = address.zipCode || "";
		address.primary = 1;

		ccPatientData.addresses = [address];
		logDebug(`Mapped address with ${mappedCount} fields`);
	}

	return mappedCount;
}

/**
 * Process custom fields from webhook data
 *
 * Identifies custom fields in the webhook payload that need separate processing.
 * These fields will be handled by the custom field synchronization system.
 *
 * @param apContact - AutoPatient contact data
 * @param unmappedFields - Array to collect unmapped field names
 */
function processCustomFields(
	apContact: GetAPContactType,
	unmappedFields: string[],
): void {
	// Check for custom fields in the AP contact data
	if (apContact.customFields && Array.isArray(apContact.customFields)) {
		const customFieldNames = apContact.customFields.map((field) => field.id);
		if (customFieldNames.length > 0) {
			unmappedFields.push(...customFieldNames);
			logDebug(
				`Found ${customFieldNames.length} custom fields for separate processing`,
			);
		}
	}

	// Check for additional fields that might be custom fields
	// (fields not in the standard AP contact structure)
	const standardFields = new Set([
		"contact_id",
		"first_name",
		"last_name",
		"full_name",
		"email",
		"phone",
		"tags",
		"country",
		"date_created",
		"full_address",
		"contact_type",
		"location",
		"workflow",
		"triggerData",
		"contact",
		"attributionSource",
		"customData",
	]);

	for (const [fieldName, fieldValue] of Object.entries(apContact)) {
		if (
			!standardFields.has(fieldName) &&
			fieldValue !== undefined &&
			fieldValue !== ""
		) {
			unmappedFields.push(fieldName);
			logDebug(`Found potential custom field: ${fieldName}`);
		}
	}
}

/**
 * Map email and phone as custom fields to CC patient
 *
 * Replicates v3Integration behavior by mapping email and phone values
 * as custom fields in addition to standard fields. This ensures compatibility
 * with existing CC custom field configurations.
 *
 * @param apContact - AutoPatient contact data
 * @param ccPatientData - CC patient data being built
 * @returns Number of custom fields mapped
 */
async function mapEmailPhoneAsCustomFields(
	apContact: GetAPContactType,
	ccPatientData: Partial<PostCCPatientType>,
): Promise<number> {
	try {
		// Import CC API client
		const { ccCustomfieldReq } = await import("@/apiClient");

		// Get all CC custom fields to match against
		const ccCustomFields = await ccCustomfieldReq.all();

		// Prepare email/phone values for custom field mapping
		const fieldValues: Record<string, string> = {};

		if (
			apContact.email !== null &&
			apContact.email !== undefined &&
			apContact.email !== ""
		) {
			fieldValues.email = apContact.email;
		}

		if (
			apContact.phone !== null &&
			apContact.phone !== undefined &&
			apContact.phone !== ""
		) {
			fieldValues.phoneMobile = apContact.phone;
			fieldValues["phone-mobile"] = apContact.phone;
			fieldValues.phone = apContact.phone;
		}

		// Match and create custom field payloads
		const customFieldPayloads: Array<{
			field: { id: string };
			values: Array<{ id: number } | { value: string }>;
		}> = [];

		Object.keys(fieldValues).forEach((fieldName) => {
			const match = ccCustomFields.find(
				(ccField) => ccField.name === fieldName || ccField.label === fieldName,
			);

			if (match && fieldValues[fieldName]) {
				const payload: {
					field: { id: string };
					values: Array<{ id: number } | { value: string }>;
				} = {
					field: { id: match.id.toString() },
					values: [{ value: fieldValues[fieldName] }],
				};

				// Handle dropdown fields with predefined values
				if (match.allowedValues && match.allowedValues.length > 0) {
					const allowedValue = match.allowedValues.find(
						(v) => v.value === fieldValues[fieldName],
					);
					if (allowedValue && "id" in allowedValue) {
						// Use the numeric ID for allowedValues references
						payload.values = [{ id: allowedValue.id }];
					}
				}

				customFieldPayloads.push(payload);
				logDebug(
					`Mapped ${fieldName} as custom field: ${fieldValues[fieldName]}`,
				);
			}
		});

		// Add custom fields to patient data if any were mapped
		if (customFieldPayloads.length > 0) {
			ccPatientData.customFields = customFieldPayloads;
			logInfo(`Mapped ${customFieldPayloads.length} email/phone custom fields`);
			return customFieldPayloads.length;
		}
	} catch (error) {
		logError("Failed to map email/phone as custom fields", error);
	}
	return 0;
}
